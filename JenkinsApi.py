import jenkins
import json

class JenkinsApi:
    def __init__(self, server_url, username, password):
        self.server_url = server_url
        self.username = username
        self.password = password
        self.jenkins_server = jenkins.<PERSON>(server_url, username, password)

    
    """
    获取QAC视图下的所有job信息
    """
    def get_qac_view_jobs(self):
        #获取QAC试图下的所有文件夹
        qac_list = []
        qac_dirs = self.jenkins_server.get_jobs(view_name="QAC")
        for dir in qac_dirs:
            if dir["_class"] == "com.cloudbees.hudson.plugins.folder.Folder":
                qac_list.append(dir["name"])
        job_list = []
        jobs = self.jenkins_server.get_jobs(folder_depth=2)
        for job in jobs:
            if job["_class"] == "com.cloudbees.hudson.plugins.folder.Folder" and job["name"] in qac_list:
                qac_jobs = job["jobs"]
                for qac_job in qac_jobs:
                    job_list.append(qac_job["fullname"])
        return job_list
    
    """
    获取job的构建所需要的参数
    """
    def get_job_info(self, jobs):
        jon_param = {}
        for job in jobs:
            params = []
            job_info = self.jenkins_server.get_job_info(job)
            if job_info["property"] is not None:
                for property in job_info["property"]:
                    if property["_class"] == "hudson.model.ParametersDefinitionProperty":
                        for parameter in property["parameterDefinitions"]:
                            params.append(parameter["defaultParameterValue"])
            jon_param[job] = params
        return jon_param
    def build(self, job_name, params):
        job_info = self.jenkins_server.get_job_info(job_name)
        if job_info.get("lastBuild") is not None:
            if job_info.get("lastBuild").get("number") is not None:
                job_build = self.jenkins_server.get_build_info(job_name,job_info.get("lastBuild").get("number"))
                if job_build.get("building"):
                    print(f"job{job_name}正在构建中")
                    return False
        print(f"开始构建{job_name}, 参数为{params}")
        result = self.jenkins_server.build_job(name=job_name, parameters=params) 
        if result:
            return True
        else:
            return False
    def get_job_laster_build_number(self,job_name):
        job_info = self.jenkins_server.get_job_info(job_name)
        return job_info.get("lastBuild").get("number")
        # print(json.dumps(self.jenkins_server.get_job_info("QAC_Chery/Chery_T19C_MCU")))
        # print("=====>",json.dumps(self.jenkins_server.get_build_info("QAC_Chery/Chery_T19C_MCU",6)))
    
    def get_build_info(self,job_name,build_number):
        return self.jenkins_server.get_build_info(job_name,build_number)
    def get_build_console_output(self,job_name,build_number):
        return self.jenkins_server.get_build_console_output(job_name,build_number)