from openai import OpenAI
from openai.types.chat import ChatCompletionMessageParam
from typing import List

class AI:
    def __init__(self):
        self.client = OpenAI(
            api_key='sk-FSO3itlzGLQrrnrjL9VE5CrckeBs6M5nvVRfHBcu3yFkhrN3', base_url='https://api.chatanywhere.com.cn')

    def send_message(self, messages: List[ChatCompletionMessageParam]):
        completion = self.client.chat.completions.create(
            model="gpt-3.5-turbo-16k",
            messages=messages,
            max_tokens=512000,
            top_p=1,
            frequency_penalty=0.0,
            presence_penalty=0.0,
            temperature=0
        )
        return completion.choices[0].message.content
