import tree_sitter

class ATS:
    def __init__(self, file_path: str) -> None:
        self.file_path = file_path
        pass

    def __generate_ATS(self) -> None:
        suffix = self.file_path.split('.')[-1]
        if suffix == 'c':
            # 加载 C 语言的语法解析器
            LANGUAGE = tree_sitter.Language('build/tree-sitter.dll', 'c')
        elif suffix == 'cpp' or suffix == 'cc':
            # 加载 C++ 语言的语法解析器
            LANGUAGE = tree_sitter.Language('build/tree-sitter.dll', 'cpp')
        else:
            raise Exception('NOT SUPPORT FILE TYPE')

        # 读取 C 文件内容
        with open(self.file_path, 'r') as file:
            code = file.read()
            self.code = code

        # 创建语法树
        parser = tree_sitter.Parser()
        parser.set_language(LANGUAGE)
        return parser.parse(bytes(code, 'utf8'))

    def get_function_list(self) -> list[str]:
        # 生成语法树
        ATS = self.__generate_ATS()

        # 递归遍历语法树并获取函数列表
        functions = []
        self.__traverse_functions(ATS.root_node, functions)

        return functions

    def __traverse_functions(self, node, functions):
        if node.type == 'function_definition':
            function_declarator = next(
                (children for children in node.children if children.type == 'function_declarator'), None)
            if not function_declarator:
                return
            function_name = next(
                (children for children in function_declarator.children if children.type == 'identifier'), None)
            functions.append({
                "code": self.code[node.start_byte: node.end_byte],
                "function_name": function_name.text.decode()
            })

        for child in node.children:
            self.__traverse_functions(child, functions)


if __name__ == '__main__':
    ats = ATS(file_path=r'D:\Projects\temp\mcu\app\app.c')
    fn_list = ats.get_function_list()
    for fn in fn_list:
        print(fn)
