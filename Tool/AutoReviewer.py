import re
from tree_sitter import Language, Parser, Tree
import difflib
import git
from Gerrit import Gerrit
from Dify import send_ai_message
import argparse


class AutoReviewer:
    def __init__(self, repo_path: str):
        self.repo_path = repo_path
        self.gerrit = Gerrit()

    def __generate_AST(self, file_path: str):
        """
        Generate an Abstract Syntax Tree (AST) by parsing a file using the Tree-sitter library.

        Args:
            file_path (str): The path to the file that needs to be parsed.

        Returns:
            Tree: The generated Abstract Syntax Tree (AST) of the parsed file.
        """
        parser = Parser()
        with open(file_path, 'r', encoding='utf-8') as f:
            self.code = f.read()
        file_suffix = file_path.split('.')[-1]
        if file_suffix == 'c':
            C_LANGUAGE = Language('build/tree-sitter.dll', 'c')
            parser.set_language(C_LANGUAGE)
        elif file_suffix == 'cpp':
            CPP_LANGUAGE = Language('build/tree-sitter.dll', 'cpp')
            parser.set_language(CPP_LANGUAGE)
        elif file_suffix == 'py':
            PY_LANGUAGE = Language('build/tree-sitter.dll', 'python')
            parser.set_language(PY_LANGUAGE)
        elif file_suffix == 'js':
            JS_LANGUAGE = Language('build/tree-sitter.dll', 'javascript')
            parser.set_language(JS_LANGUAGE)
        elif file_suffix == 'ts':
            TS_LANGUAGE = Language('build/tree-sitter.dll', 'typescript')
            parser.set_language(TS_LANGUAGE)
        else:
            return
        AST = parser.parse(bytes(self.code, "utf8"))

        # Get the root node
        return AST

    def __search_tree_node_name_by_line_number(self, AST: Tree, min_line_number: int, max_line_number: int):
        if AST is None:
            return
        root_node = AST.root_node
        diff_code = []
        for i in range(root_node.child_count):
            child = root_node.child(i)
            start_line = child.start_point[0]
            end_line = child.end_point[0]
            if child.type == 'comment' or start_line > max_line_number or end_line < min_line_number:
                pass
            else:
                diff_code.append(self.code[child.start_byte:child.end_byte])
        return "\n".join(set(diff_code))

    def __get_commit_diff(self, commit_id: str = ''):
        repo = git.Repo(self.repo_path)
        diffs = []
        last_commit = repo.commit(commit_id)
        file_diffs = last_commit.diff(last_commit.parents[0])
        diff_blocks = []
        for file_diff in [file_diff for file_diff in file_diffs if file_diff.a_path.endswith(('.c', '.cpp', '.py'))]:
            line_number = 0
            AST = self.__generate_AST(
                file_path=f'{self.repo_path}/{file_diff.a_path}')
            a_content = file_diff.a_blob.data_stream.read().decode(
                "utf-8") if file_diff.a_blob else ''
            b_content = file_diff.b_blob.data_stream.read().decode(
                "utf-8") if file_diff.b_blob else ''
            differ = difflib.unified_diff(
                a_content.splitlines(), b_content.splitlines(), lineterm='')

            start_catch = False
            for line in differ:
                if line.startswith("@@"):
                    start_catch = True
                    if len(diff_blocks) > 0 and diff_code != '':
                        diff_block = '\n'.join(diff_blocks)
                        diffs.append({
                            "diff_block": diff_block,
                            "diff_code": diff_code,
                            "commit_message": [line.split('#')[-1].strip() for line in last_commit.message.splitlines() if '[Trace Info]' in line][0],
                            "path": file_diff.a_path,
                            "line_number": line_number
                        })
                    diff_blocks = []
                    pattern = r'@@ -(\d+),(\d+) \+(\d+),(\d+) @@'
                    ranges = re.findall(pattern, line)
                    if len(ranges) == 0:
                        continue
                    for r in ranges:
                        original_start, original_length, modified_start, modified_length = map(
                            int, r)
                        line_number = original_start
                        original_end = original_start + original_length - 1
                        modified_end = modified_start + modified_length - 1
                    diff_code = self.__search_tree_node_name_by_line_number(
                        AST=AST,
                        min_line_number=min([original_start, modified_start]),
                        max_line_number=max([original_end, modified_end])
                    )
                else:
                    if start_catch:
                        diff_blocks.append(line)
        if len(diff_blocks) > 0 and diff_code != '':
            diff_block = '\n'.join(diff_blocks)
            diffs.append({
                "diff_block": diff_block,
                "diff_code": diff_code,
                "commit_message": [line.split('#')[-1].strip() for line in last_commit.message.splitlines() if '[Trace Info]' in line][0],
                "path": file_diff.a_path,
                "line_number": "NA"
            })
        return diffs

    def auto_review(self, commit_id: str):
        diffs = self.__get_commit_diff(commit_id=commit_id)
        # tasks = []
        if len(diffs) > 30:
            print("Too many commits, only scan top 30 commits!")
            diffs = diffs[:30]
        for diff in diffs:
            file = diff['path']
            line = diff['line_number']

            start_message = f"Review for {file}, line {line}:\n"
            ai_message = send_ai_message(inputs={
                "code": diff['diff_code'],
                "commit_msg": diff['commit_message'],
                "diff": diff['diff_block']
            }, user='AutoReview',
                response_mode='blocking',
                token='app-nARkvroJtjPabimFSWM9pevx')
            if not ai_message:
                continue
            message = start_message + ai_message
            print(message)
            self.gerrit.send_comment(comment=message, commit_id=commit_id)


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Auto Reviewer')
    parser.add_argument('--repo-path', type=str, required=True,
                        help='The path of the repository')
    parser.add_argument('--commit-id', type=str,
                        required=True, help='The commit id to review')
    args = parser.parse_args()
    repo_path = args.repo_path
    commit_id = args.commit_id
    auto_reviewer = AutoReviewer(
        repo_path=repo_path)
    auto_reviewer.auto_review(
        commit_id=commit_id)
