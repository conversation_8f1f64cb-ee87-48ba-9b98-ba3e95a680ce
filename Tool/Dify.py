import json
import requests


def send_ai_message(*, inputs, response_mode='streaming', user='UnitTestGenerator', token):
    answer = ''
    try:
        response = requests.post(
            url="http://10.179.48.141:8383/v1/completion-messages",
            headers={
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {token}'
            },
            data=json.dumps({
                'inputs': inputs,
                'response_mode': response_mode,
                'user': user
            }),
            stream=response_mode == 'streaming'
        )
        # 检查响应是否成功
        if response.status_code == 200:
            if response_mode == 'streaming':
                # 以流式方式读取响应内容
                for chunk in response.iter_content(chunk_size=None, decode_unicode=True):
                    # 处理每个数据块
                    # 将字节转换为字符串
                    if 'event: ping' in chunk:
                        continue
                    if '"event": "message_end"' in chunk:
                        break
                    try:
                        data_chunk = chunk.strip().split('data: ')[1]
                        result = json.loads(data_chunk)
                        print(result['answer'], end='')
                        answer += result['answer']
                    except Exception as e:
                        print(fr'{chunk}'.replace('data:', ''))
                        print(e)
                        break
                    # # 解析 JSON 数据
                    # answer += data['answer']
                    # print(data['answer'], end='')
            else:
                # print(response.json()['answer'])
                return response.json()['answer']
        else:
            print("请求失败:", response.status_code)
            print(response.text)
    except Exception as e:
        print(e)
    return answer


if __name__ == '__main__':
    send_ai_message(inputs={
        'code': """
void test_Telltale_Process(void)
{    
    boolean bool_s_Tw53CConfigFlag = FALSE;
    boolean bool_s_T_IMCConfigFlag = FALSE;
    U16 u16_s_TT_Imc_time = FALSE;
    
    
    bool_s_Tw53CConfigFlag = b_g_RohmGetImcDelayStatus();
    if((FALSE == bool_s_T_IMCConfigFlag) &&(TRUE == u8_s_wakeUpSts))
    {
        bool_s_T_IMCConfigFlag = TRUE;
        Fee_ReadPoll(TT_Imc_time, (uint8 *)&u16_s_TT_Imc_time, VALUE_1);
        if((TTValue_0 <= u16_s_TT_Imc_time ) && (TTValue_200 >= u16_s_TT_Imc_time ))
        {
            u8_TIMC_time = (U8)((u16_s_TT_Imc_time*1000/10) * 2 / TT_TASK_TIME);
        }
    }
    
    if(TRUE == bool_s_Tw53CConfigFlag)
    {
        Telltale_Timer();
        TELLTALE_ID_DEF_e id = e_Invalid_Id;
        for (int i = id = e_Invalid_Id; i < e_MaxID; i++)
        {
            Telltale_Judge(i);
        }
        Telltale_Output();
    }
    else
    {/*Nothing*/}
}
""",
        'filename': 'test'
    })
