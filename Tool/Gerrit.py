import subprocess


class Gerrit:
    # 设置Gerrit服务器的URL和变更ID
    ssh_command_basic = 'ssh -p 29418 ************'

    def __init__(self) -> None:
        pass

    def send_comment(self, comment: str, commit_id: str):
        # 设置API端点URL
        command = [
            *self.ssh_command_basic.split(),
            'gerrit',
            'review',
            commit_id,
            '-m',
            f'"{comment}"',
            '--notify',
            'NONE'  # 设置通知方式，这里设置为不通知
        ]

        process = subprocess.Popen(
            command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        # 读取输出
        output, error = process.communicate()
        print(output.decode('utf-8'))
        print(error.decode('utf-8'))


if __name__ == '__main__':
    gerrit = Gerrit()
    gerrit.send_comment(
        '测试评论', commit_id='b0af9967cc5dd017ca074f627908783a95f1eb66')
