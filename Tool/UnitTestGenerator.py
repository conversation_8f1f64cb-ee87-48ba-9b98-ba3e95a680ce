from AI import AI
from ATS import ATS
import os
import argparse


class UnitTestGenerator:
    def __init__(self, repo_path: str):
        self.ai = AI()
        self.repo_path = repo_path
        self.prompt = """
I would like you to help me design test cases for the following function using Google Test fixtures. 
The generated test cases should cover all conditional branch judgments, and the coverage rate should reach 100%. 
Note that in addition to 'if' conditional coverage, loop conditions such as 'for' and 'while' also need to be covered. 
I need you to return the complete Google Test code and Google Mock code that covers all scenarios. 
Please add detailed comments to each test function in the code to explain which test case it covers and detail the purpose of the test.

Suggestions:

Include specific examples of the function's inputs and expected outputs to help define the test cases.
Consider different edge cases when designing the test cases.
Think about potential error conditions that the function might need to handle and include these in the test cases.
You should use GMock to mock key behaviors and important side effects that directly impact the test results.
Your answer should only contain the cpp code.
Here is the source function:
"""

    def auto_generate(self, file_path: str):
        ats = ATS(file_path=f'{self.repo_path}/{file_path}')
        with open(f'{self.repo_path}/{file_path}', 'r', encoding='utf-8') as file:
            source = file.read()
        fn_list = ats.get_function_list()
        for fn in fn_list:
            print('===============================fn==============================')
            code = fn['code']
            function_name = fn['function_name']
            print(function_name)
            response = self.ai.send_message(messages=[{
                'role': 'user',
                'content': f'{self.prompt}{code}'
            }])
            print('===============================response==============================')
            try:
                if not os.path.exists(f'{self.repo_path}/unit_tests/{file_path}'):
                    # 创建文件夹
                    os.makedirs(f'{self.repo_path}/unit_tests/{file_path}')
                if '```' in response:
                    test_code = '\n'.join(
                        response.split('```')[1].split('\n')[1:])
                else:
                    test_code = response
                # 写入Mock文件
                file_name = file_path.replace(
                    '/', ' ').replace('\\', ' ').split(' ')[-1]
                with open(f'{self.repo_path}/unit_tests/{file_path}/mock_{file_name}', 'w', encoding='utf-8') as file:
                    file.write(source.replace('static ', ''))
                with open(f'{self.repo_path}/unit_tests/{file_path}/test_{function_name}.cpp', 'w', encoding='utf-8') as file:
                    file.write(test_code)
                print(response)
            except Exception as e:
                print(
                    '==========================error response=========================')
                # print(response)
                print(e)


if __name__ == '__main__':
    # 创建参数解析器
    parser = argparse.ArgumentParser(description='AutoGUI')

    # 添加命令行参数
    parser.add_argument('--repo-path', help='待分析的仓库', required=True)
    parser.add_argument('--file-path', help='待分析的文件路径（相对仓库路径）', required=True)
    args = parser.parse_args()

    # D:\Projects\temp\mcu
    unit_test_generator = UnitTestGenerator(repo_path=args.repo_path)
    # platform\service\canstack\CanStackTimer.c
    unit_test_generator.auto_generate(
        file_path=args.file_path)
