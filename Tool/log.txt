void AppRun_Init(void)
{
	EVHD_vRegisterEvent(Ev_nApp_Status_Event_App_vEventHandler);
	EVHD_vRegisterEvent(Ev_nSMInit_Flash_System_vEventHandler);  
	EVHD_vRegisterEvent(Ev_nSMInit_Courier_System_vEventHandler);  
	EVHD_vRegisterEvent(Ev_nSMDeInit_Flash_System_vEventHandler);
	EVHD_vRegisterEvent(Ev_nSMDeInit_Courier_System_vEventHandler);

	/*Fee test code*/
	uint8 read_ret = 0;
	read_ret = Fee_ReadPoll(1, (uint8 *)&blk1_read, 16);
	read_ret = Fee_ReadPoll(12, (uint8 *)&blk12_read, 256);
	read_ret = Fee_ReadPoll(14, (uint8 *)&blk14_read, 256);
	read_ret = Fee_ReadPoll(16, (uint8 *)&blk16_read, 256);
	ssdk_printf(0, "blk 1 = 0x%x \r\n", blk1_read.ODO_ClearFlag);
	ssdk_printf(0, "blk 12 = 0x%x \r\n", blk12_read.BoschProdInfo1.K1);
	ssdk_printf(0, "blk 14 = 0x%x \r\n", blk14_read.ICU_IVI_Fault_Info.TFTFailCounter_ICU);
	ssdk_printf(0, "blk 16 = 0x%x \r\n", blk16_read.ECC_BackLight.Dimm_PWM_DutyCycle0);
	/*Fee test code*/

}
void AppRun_1ms(void)
{
	/* empty code */
}
void AppRun_5ms(void)
{
	// need request in ECUM
	static bool hardwareInitFlag = false;
	//if(hardwareInitFlag == false)
	{
		// (void)CSHD_enSetRequest(CSHD_nRequestHardwareByDIO);//request hardware
		if(IOA_ReadDigitalInputPin_b(IOA_IN_KL15_INT) == 1)
		{
			(void)CSHD_enSetRequest(CSHD_nRequestActiveByIGN);//request hardware
			hardwareInitFlag = true;
		}
		else
		{
			CSHD_vClearRequest(CSHD_nRequestActiveByIGN);
			hardwareInitFlag = false;
		}
	}
}
void AppRun_10ms(void)
{
	static uint8_t count10ms = 0;
	IOA_SetPwmDuty_v(IOA_PWM_ICU_TFT_BL_PWM, 10000u);
	count10ms++;
	if(count10ms > 50) //0.5s reach, trigger LED1
	{
		count10ms = 0;
	}
}
void AppRun_25ms(void)
{
	static uint8_t count25ms = 0;
	count25ms++;
	if(count25ms > 40) //1s reach, trigger LED2
	{
		count25ms = 0;
	}
	/*dpool test code*/
	DPool_enSetDpool_test(count25ms);
	/*dpool test code*/
}
void AppRun_100ms(void)
{
	
	static uint8_t count100ms = 0;

	count100ms++;
	if(count100ms > 100) //10s reach, send evnet
	{
		EVHD_vSendEvent(Ev_nApp_Status_Event);
		count100ms = 0;
	}

	/*dpool test code*/
	static uint8 Dpool_test_val = 0;
	DPool_enGetDpool_test(Dpool_test_val);
	// ssdk_printf(0, "task running,dpool data = %d \r\n", Dpool_test_val);
	/*dpool test code*/
}
void App_vEventHandler(EVHD_tenRecvEvent enEvent)
{
	switch(enEvent)
	{
		case Ev_nApp_Status_Event_App_vEventHandler:
		    break;
			
		default:
		    break;
	}
}
void System_vEventHandler(EVHD_tenRecvEvent enEvent)
{
	switch(enEvent)
	{
		case Ev_nSMInit_Display_System_vEventHandler:
		    break;
		case Ev_nSMInit_Flash_System_vEventHandler:
			EVHD_vSendEvent(Ev_nSMOnFeedback_Flash);
			break;
		case Ev_nSMInit_Courier_System_vEventHandler:
			EVHD_vSendEvent(Ev_nSMOnFeedback_Courier);
			break;
		case Ev_nSMDeInit_Flash_System_vEventHandler:
		    EVHD_vSendEvent(Ev_nSMOffFeedback_Flash);
		    break;

		case Ev_nSMDeInit_Courier_System_vEventHandler:
		    EVHD_vSendEvent(Ev_nSMOffFeedback_Courier);
		    break;

		default:
		    break;
	}
}
void Hmi_vProcessState(EVHD_tenRecvEvent enEvent)
{

}
void DRC_vEventHandler(EVHD_tenRecvEvent enEvent)
{

}
void Hmi_vEventHandler(EVHD_tenRecvEvent enEvent)
{
	
}
void setEEDate(uint8 *addr, uint8 len)
{
	if(len < 2 )
		return;

	switch (addr[0])
	{
	case 00:
		blk1_write.ODO_ClearFlag = addr[1];
		blk12_write.BoschProdInfo1.K1 = addr[1] + 1;
		blk14_write.ICU_IVI_Fault_Info.TFTFailCounter_ICU = addr[1] + 2;
		blk16_write.ECC_BackLight.Dimm_PWM_DutyCycle0 = addr[1] + 3;
		/*FeeM_Req_Write(1, (uint8 *)&blk1_write, NULL);
		FeeM_Req_Write(12, (uint8 *)&blk12_write, NULL);
		FeeM_Req_Write(14, (uint8 *)&blk14_write, NULL);
		FeeM_Req_Write(16, (uint8 *)&blk16_write, NULL);*/
		break;
	default:
		break;
	}
}
