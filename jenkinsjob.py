import requests
import json
import jenkins
import argparse
from datetime import datetime, timezone, timedelta

# Jenkins 服务器信息
server_url = "http://10.179.48.136:8081"
username = "sup1whu"
password = "11399430c14cd6a8002f2060e9d2ad7c05"

jenkins_server = jen<PERSON>.<PERSON>(server_url, username, password)

# Jenkins Job 相关信息
JENKINS_JOBS = {
    'android': "Compile_Chery/Compile_Mtk8678_Android_System_daily_build",
    'yocto': "Compile_Chery/Compile_Mtk8678_Yocto_System_daily_build"
}

def get_build_info(job_name, build_number):
    """ 获取 Jenkins Job 的构建信息 """
    return jenkins_server.get_build_info(job_name, build_number)

def get_previous_builds(job_name, days_count = 7):
    """获取最近一周内的构建号范围（从7天前的00:00开始）"""
    job_info = jenkins_server.get_job_info(job_name)
    last_build_number = job_info['lastBuild']['number']
    
    # 获取当前时间的午夜时间点（00:00）
    now = datetime.now(timezone.utc)
    today_midnight = now.replace(hour=0, minute=0, second=0, microsecond=0)
    # 计算7天前的午夜时间点
    start_time = today_midnight - timedelta(days=days_count)
    
    # 从最新的构建开始往前查找
    start_build = None
    end_build = last_build_number
    
    for build_number in range(last_build_number, 0, -1):
        try:
            build_info = get_build_info(job_name, build_number)
            build_timestamp = build_info.get('timestamp', 0) / 1000  # 转换为秒
            build_time = datetime.fromtimestamp(build_timestamp, timezone.utc)
            
            if build_time < start_time:
                start_build = build_number
                break
        except jenkins.NotFoundException:
            continue
        
    if start_build is None:
        start_build = end_build - 50  # 如果找不到一周前的构建，默认取最近50个构建
    
    return start_build, end_build

def convert_timestamp(timestamp_ms):
    """ 将毫秒级时间戳转换为东八区（北京时间） """
    timestamp_sec = timestamp_ms // 1000
    utc_time = datetime.fromtimestamp(timestamp_sec, tz=timezone.utc)
    beijing_time = utc_time.astimezone(timezone(timedelta(hours=8)))
    return beijing_time.strftime("%Y-%m-%d %H:%M:%S")

def parse_jenkins_log(log):
    """ 解析 Jenkins Job 日志 """
    execution_time = convert_timestamp(log.get('timestamp', 0))  # 执行时间（转换北京时间）
    job_number = log.get('number', 'Unknown')  # 任务编号
    duration_ms = log.get('duration', 0)  # 获取执行时长（毫秒）
    duration_min = round(duration_ms / (1000 * 60), 2)  # 转换为分钟，保留 2 位小数
    job_result = log.get('result', 'UNKNOWN')  # Job 执行结果
    
    queue_info = next((action for action in log.get('actions', []) 
                      if action.get('_class') == 'jenkins.metrics.impl.TimeInQueueAction'), {})
    waiting_time_ms = queue_info.get('waitingDurationMillis', 0)
    waiting_time_min = round(waiting_time_ms / (1000 * 60), 2)
    
    return "\t".join(str(x) for x in [
        job_number,
        execution_time,
        waiting_time_min,
        job_result,
        duration_min
    ])

def main():
    """ 主函数，获取 Jenkins 任务等待时间 """
    parser = argparse.ArgumentParser(description='获取Jenkins构建信息')
    
    # 添加参数
    group = parser.add_mutually_exclusive_group()
    group.add_argument('-a', '--android', action='store_true', 
                      help='获取Android构建信息')
    group.add_argument('-y', '--yocto', action='store_true', 
                      help='获取Yocto构建信息')
    parser.add_argument('-d', '--days', type=int, default=7, 
                      help='指定要查询的天数，默认为7天')
    
    # 解析参数
    try:
        args = parser.parse_args()
    except argparse.ArgumentError as e:
        parser.error(str(e))
    
    # 检查是否指定了任务类型
    if not (args.android or args.yocto):
        parser.error('请至少指定一个任务类型: -a (Android) 或 -y (Yocto)')
    
    # 确定要检查的任务
    job_type = 'Android' if args.android else 'Yocto'
    job_name = JENKINS_JOBS['android'] if args.android else JENKINS_JOBS['yocto']    
    try:
        start_build, end_build = get_previous_builds(job_name, args.days)
        
        for build_number in range(start_build, end_build + 1):
            try:
                build_info = get_build_info(job_name, build_number)
                print(parse_jenkins_log(build_info))
            except jenkins.NotFoundException:
                print(f"构建 #{build_number} 不存在，跳过")
                continue
            
    except Exception as e:
        print(f"获取{job_type}任务信息时发生错误: {str(e)}")

if __name__ == "__main__":
    main()

