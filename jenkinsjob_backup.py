import requests
import json
import jenkins
from datetime import datetime, timezone, timedelta

# Jenkins 服务器信息
server_url = "http://10.179.48.136:8081"
username = "sup1whu"
password = "11399430c14cd6a8002f2060e9d2ad7c05"

jenkins_server = jen<PERSON>.<PERSON>(server_url, username, password)

# Jenkins Job 相关信息
ANDROID_JOB_NAME = "Compile_Chery/Compile_Mtk8678_Android_System_daily_build"  
YOCTO_JOB_NAME = "Compile_Chery/Compile_Mtk8678_Yocto_System_daily_build"
BUILD_NUMBER_START = 104  # 需要查询的构建编号--开始
BUILD_NUMBER_END = 153  # 需要查询的构建编号——结束

# http://10.179.48.136:8081/view/Compile/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/88/api/json?pretty=true


def get_build_info(job_name, build_number):
    """ 获取 Jenkins Job 的构建信息 """
    return jenkins_server.get_build_info(job_name,build_number)


def convert_timestamp(timestamp_ms):
    """ 将毫秒级时间戳转换为东八区（北京时间） """
    timestamp_sec = timestamp_ms // 1000  # 转换为秒级时间戳
    utc_time = datetime.fromtimestamp(timestamp_sec, tz=timezone.utc)  # 转换为 UTC 时间
    beijing_time = utc_time.astimezone(timezone(timedelta(hours=8)))  # 转换为北京时间
    return beijing_time.strftime("%Y-%m-%d %H:%M:%S")

def parse_jenkins_log(log):
    """ 解析 Jenkins Job 日志 """
    execution_time = convert_timestamp(log.get('timestamp', 0))  # 执行时间（转换北京时间）
    job_number = log.get('number', 'Unknown')  # 任务编号
    duration_ms = log.get('duration', 0)  # 获取执行时长（毫秒）
    duration_min = round(duration_ms / (1000 * 60), 2)  # 转换为分钟，保留 2 位小数
    job_result = log.get('result', 'UNKNOWN')  # Job 执行结果
    
    # 获取排队、等待时间
    queue_info = next((action for action in log.get('actions', []) if action.get('_class') == 'jenkins.metrics.impl.TimeInQueueAction'), {})
    waiting_time_ms = queue_info.get('waitingDurationMillis', 0)  # 等待时间（毫秒）
    waiting_time_min = round(waiting_time_ms / (1000 * 60), 2)  # 转换为分钟
    
    # 输出解析结果
    result = {
        "执行时间（北京时间）": execution_time,
        "任务编号": job_number,
        "Job 执行时长（分钟）": duration_min,
        "Job 执行结果": job_result,
        "Job 排队等待时长（分钟）": waiting_time_min
    }
    
    return result

def main():
    """ 主函数，获取 Jenkins 任务等待时间 """
    # loop build_number from BUILD_NUMBER to 106
    for build_number in range(BUILD_NUMBER_START, BUILD_NUMBER_END + 1):
        build_info = get_build_info(YOCTO_JOB_NAME, build_number)
        print(parse_jenkins_log(build_info))
   
if __name__ == "__main__":
    main()

