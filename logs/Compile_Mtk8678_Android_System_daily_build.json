{"build_80": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 7450251, "executingTimeMillis": 7448319, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 10, "waitingTimeMillis": 10}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#80", "duration": 7450251, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #80", "id": "80", "keepLog": false, "number": 80, "queueId": 252438, "result": "SUCCESS", "timestamp": 1741327415085, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/80/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 81, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/81/"}, "previousBuild": {"number": 79, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/79/"}}, "build_81": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 7135444, "executingTimeMillis": 7106701, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 6, "waitingTimeMillis": 6}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#81", "duration": 7135444, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #81", "id": "81", "keepLog": false, "number": 81, "queueId": 253880, "result": "SUCCESS", "timestamp": 1741363336969, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/81/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 82, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/82/"}, "previousBuild": {"number": 80, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/80/"}}, "build_82": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 7653600, "executingTimeMillis": 7351596, "executorUtilization": 0.96, "subTaskCount": 1, "waitingDurationMillis": 7, "waitingTimeMillis": 7}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#82", "duration": 7653600, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #82", "id": "82", "keepLog": false, "number": 82, "queueId": 255846, "result": "SUCCESS", "timestamp": 1741416723015, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/82/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 83, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/83/"}, "previousBuild": {"number": 81, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/81/"}}, "build_83": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 7696670, "executingTimeMillis": 7491843, "executorUtilization": 0.97, "subTaskCount": 1, "waitingDurationMillis": 10, "waitingTimeMillis": 10}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#83", "duration": 7696670, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #83", "id": "83", "keepLog": false, "number": 83, "queueId": 257275, "result": "SUCCESS", "timestamp": 1741477972177, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/83/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 84, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/84/"}, "previousBuild": {"number": 82, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/82/"}}, "build_84": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 10332516, "executingTimeMillis": 10063913, "executorUtilization": 0.97, "subTaskCount": 1, "waitingDurationMillis": 10, "waitingTimeMillis": 10}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#84", "duration": 10332516, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #84", "id": "84", "keepLog": false, "number": 84, "queueId": 259234, "result": "SUCCESS", "timestamp": 1741615846095, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/84/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 85, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/85/"}, "previousBuild": {"number": 83, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/83/"}}, "build_85": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 9329560, "executingTimeMillis": 9289075, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 1, "waitingTimeMillis": 1}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#85", "duration": 9329560, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #85", "id": "85", "keepLog": false, "number": 85, "queueId": 260583, "result": "SUCCESS", "timestamp": 1741670445305, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/85/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 86, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/86/"}, "previousBuild": {"number": 84, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/84/"}}, "build_86": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 14180575, "executingTimeMillis": 12045532, "executorUtilization": 0.85, "subTaskCount": 1, "waitingDurationMillis": 8, "waitingTimeMillis": 8}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#86", "duration": 14180575, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #86", "id": "86", "keepLog": false, "number": 86, "queueId": 262164, "result": "SUCCESS", "timestamp": 1741677658444, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/86/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 87, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/87/"}, "previousBuild": {"number": 85, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/85/"}}, "build_87": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 21761688, "executingTimeMillis": 9216639, "executorUtilization": 0.42, "subTaskCount": 1, "waitingDurationMillis": 1, "waitingTimeMillis": 1}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#87", "duration": 21761688, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #87", "id": "87", "keepLog": false, "number": 87, "queueId": 263379, "result": "SUCCESS", "timestamp": 1741679294142, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/87/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 88, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/88/"}, "previousBuild": {"number": 86, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/86/"}}, "build_88": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 38542939, "executingTimeMillis": 7757689, "executorUtilization": 0.2, "subTaskCount": 1, "waitingDurationMillis": 0, "waitingTimeMillis": 0}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#88", "duration": 38542939, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #88", "id": "88", "keepLog": false, "number": 88, "queueId": 265066, "result": "SUCCESS", "timestamp": 1741681953697, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/88/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 89, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/89/"}, "previousBuild": {"number": 87, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/87/"}}, "build_89": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 40704612, "executingTimeMillis": 7364846, "executorUtilization": 0.18, "subTaskCount": 1, "waitingDurationMillis": 2, "waitingTimeMillis": 2}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#89", "duration": 40704612, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #89", "id": "89", "keepLog": false, "number": 89, "queueId": 266472, "result": "SUCCESS", "timestamp": 1741708920720, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/89/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 90, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/90/"}, "previousBuild": {"number": 88, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/88/"}}, "build_90": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 27775662, "executingTimeMillis": 8398693, "executorUtilization": 0.3, "subTaskCount": 1, "waitingDurationMillis": 0, "waitingTimeMillis": 0}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#90", "duration": 27775662, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #90", "id": "90", "keepLog": false, "number": 90, "queueId": 268104, "result": "SUCCESS", "timestamp": 1741759320712, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/90/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 91, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/91/"}, "previousBuild": {"number": 89, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/89/"}}, "build_91": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 10613257, "executingTimeMillis": 7583297, "executorUtilization": 0.71, "subTaskCount": 1, "waitingDurationMillis": 0, "waitingTimeMillis": 0}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#91", "duration": 10613257, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #91", "id": "91", "keepLog": false, "number": 91, "queueId": 268338, "result": "SUCCESS", "timestamp": 1741795320714, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/91/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 92, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/92/"}, "previousBuild": {"number": 90, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/90/"}}, "build_92": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UserIdCause", "shortDescription": "Started by user 陈建", "userId": "chj1whu", "userName": "陈建"}]}, {}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 7175557, "executingTimeMillis": 7174727, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 0, "waitingTimeMillis": 0}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#92", "duration": 7175557, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #92", "id": "92", "keepLog": false, "number": 92, "queueId": 268687, "result": "SUCCESS", "timestamp": 1741848651072, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/92/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 93, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/93/"}, "previousBuild": {"number": 91, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/91/"}}, "build_93": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UserIdCause", "shortDescription": "Started by user 陈建", "userId": "chj1whu", "userName": "陈建"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 4870, "executingTimeMillis": 4870, "executorUtilization": 1.0, "subTaskCount": 0, "waitingDurationMillis": 1, "waitingTimeMillis": 1}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "jenkins.model.InterruptedBuildAction"}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#93", "duration": 4870, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #93", "id": "93", "keepLog": false, "number": 93, "queueId": 268759, "result": "ABORTED", "timestamp": 1741859807275, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/93/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 94, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/94/"}, "previousBuild": {"number": 92, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/92/"}}, "build_94": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 7112218, "executingTimeMillis": 7111695, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 0, "waitingTimeMillis": 0}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#94", "duration": 7112218, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #94", "id": "94", "keepLog": false, "number": 94, "queueId": 268929, "result": "SUCCESS", "timestamp": 1741881720713, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/94/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 95, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/95/"}, "previousBuild": {"number": 93, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/93/"}}, "build_95": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 7363659, "executingTimeMillis": 7361954, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 0, "waitingTimeMillis": 0}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#95", "duration": 7363659, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #95", "id": "95", "keepLog": false, "number": 95, "queueId": 269248, "result": "SUCCESS", "timestamp": 1741932120713, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/95/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 96, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/96/"}, "previousBuild": {"number": 94, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/94/"}}, "build_96": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 9960966, "executingTimeMillis": 9960424, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 0, "waitingTimeMillis": 0}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#96", "duration": 9960966, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #96", "id": "96", "keepLog": false, "number": 96, "queueId": 269509, "result": "SUCCESS", "timestamp": 1741968120714, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/96/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 97, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/97/"}, "previousBuild": {"number": 95, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/95/"}}, "build_97": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 7684847, "executingTimeMillis": 7682937, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 0, "waitingTimeMillis": 0}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#97", "duration": 7684847, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #97", "id": "97", "keepLog": false, "number": 97, "queueId": 269802, "result": "SUCCESS", "timestamp": 1742018520712, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/97/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 98, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/98/"}, "previousBuild": {"number": 96, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/96/"}}, "build_98": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 7055529, "executingTimeMillis": 7055206, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 0, "waitingTimeMillis": 0}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#98", "duration": 7055529, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #98", "id": "98", "keepLog": false, "number": 98, "queueId": 270037, "result": "SUCCESS", "timestamp": 1742054520713, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/98/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 99, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/99/"}, "previousBuild": {"number": 97, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/97/"}}, "build_99": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 7024075, "executingTimeMillis": 7022283, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 0, "waitingTimeMillis": 0}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#99", "duration": 7024075, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #99", "id": "99", "keepLog": false, "number": 99, "queueId": 270326, "result": "SUCCESS", "timestamp": 1742104920712, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/99/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 100, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/100/"}, "previousBuild": {"number": 98, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/98/"}}, "build_100": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 7054810, "executingTimeMillis": 7054263, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 0, "waitingTimeMillis": 0}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#100", "duration": 7054810, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #100", "id": "100", "keepLog": false, "number": 100, "queueId": 270559, "result": "SUCCESS", "timestamp": 1742140920712, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/100/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 101, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/101/"}, "previousBuild": {"number": 99, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/99/"}}, "build_101": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 7011369, "executingTimeMillis": 7009162, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 0, "waitingTimeMillis": 0}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#101", "duration": 7011369, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #101", "id": "101", "keepLog": false, "number": 101, "queueId": 270865, "result": "SUCCESS", "timestamp": 1742191320713, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/101/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 102, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/102/"}, "previousBuild": {"number": 100, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/100/"}}, "build_102": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 15827074, "executingTimeMillis": 15826572, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 0, "waitingTimeMillis": 0}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#102", "duration": 15827074, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #102", "id": "102", "keepLog": false, "number": 102, "queueId": 271148, "result": "FAILURE", "timestamp": 1742227320741, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/102/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 103, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/103/"}, "previousBuild": {"number": 101, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/101/"}}, "build_103": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 306377, "executingTimeMillis": 306377, "executorUtilization": 1.0, "subTaskCount": 0, "waitingDurationMillis": 0, "waitingTimeMillis": 0}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "jenkins.model.InterruptedBuildAction"}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#103", "duration": 306377, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #103", "id": "103", "keepLog": false, "number": 103, "queueId": 271453, "result": "ABORTED", "timestamp": 1742277720712, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/103/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 104, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/104/"}, "previousBuild": {"number": 102, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/102/"}}, "build_104": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UserIdCause", "shortDescription": "Started by user sup1whu", "userId": "sup1whu", "userName": "sup1whu"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 6235614, "executingTimeMillis": 6234660, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 0, "waitingTimeMillis": 0}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#104", "duration": 6235614, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #104", "id": "104", "keepLog": false, "number": 104, "queueId": 271499, "result": "SUCCESS", "timestamp": 1742285314617, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/104/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 105, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/105/"}, "previousBuild": {"number": 103, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/103/"}}, "build_105": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 6130993, "executingTimeMillis": 6130435, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 0, "waitingTimeMillis": 0}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#105", "duration": 6130993, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #105", "id": "105", "keepLog": false, "number": 105, "queueId": 271694, "result": "SUCCESS", "timestamp": 1742313720713, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/105/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 106, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/106/"}, "previousBuild": {"number": 104, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/104/"}}, "build_106": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.triggers.TimerTrigger$TimerTriggerCause", "shortDescription": "Started by timer"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 6171097, "executingTimeMillis": 6169364, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 0, "waitingTimeMillis": 0}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Android_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#106", "duration": 6171097, "estimatedDuration": 6179235, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Android_System_daily_build #106", "id": "106", "keepLog": false, "number": 106, "queueId": 272014, "result": "SUCCESS", "timestamp": 1742364120712, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/106/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": null, "previousBuild": {"number": 105, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/105/"}}}