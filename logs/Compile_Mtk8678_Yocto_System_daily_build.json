{"build_80": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 78", "upstreamBuild": 78, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 15055851, "executingTimeMillis": 15055156, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 8496, "waitingTimeMillis": 8496}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#80", "duration": 15055851, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #80", "id": "80", "keepLog": false, "number": 80, "queueId": 249453, "result": "SUCCESS", "timestamp": 1741252072959, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/80/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 81, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/81/"}, "previousBuild": {"number": 79, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/79/"}}, "build_81": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 79", "upstreamBuild": 79, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 10370217, "executingTimeMillis": 10369678, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 6412, "waitingTimeMillis": 6412}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#81", "duration": 10370217, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #81", "id": "81", "keepLog": false, "number": 81, "queueId": 250747, "result": "SUCCESS", "timestamp": 1741284107397, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/81/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 82, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/82/"}, "previousBuild": {"number": 80, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/80/"}}, "build_82": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 80", "upstreamBuild": 80, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 10588486, "executingTimeMillis": 10587756, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 8401, "waitingTimeMillis": 8401}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#82", "duration": 10588486, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #82", "id": "82", "keepLog": false, "number": 82, "queueId": 252738, "result": "SUCCESS", "timestamp": 1741334873845, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/82/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 83, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/83/"}, "previousBuild": {"number": 81, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/81/"}}, "build_83": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 81", "upstreamBuild": 81, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 10447210, "executingTimeMillis": 10408998, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 6376, "waitingTimeMillis": 6376}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#83", "duration": 10447210, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #83", "id": "83", "keepLog": false, "number": 83, "queueId": 254146, "result": "SUCCESS", "timestamp": 1741370509899, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/83/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 84, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/84/"}, "previousBuild": {"number": 82, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/82/"}}, "build_84": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 82", "upstreamBuild": 82, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 14455486, "executingTimeMillis": 14254850, "executorUtilization": 0.99, "subTaskCount": 1, "waitingDurationMillis": 17367, "waitingTimeMillis": 17367}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#84", "duration": 14455486, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #84", "id": "84", "keepLog": false, "number": 84, "queueId": 256055, "result": "SUCCESS", "timestamp": 1741424541975, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/84/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 85, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/85/"}, "previousBuild": {"number": 83, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/83/"}}, "build_85": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 83", "upstreamBuild": 83, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 11091251, "executingTimeMillis": 10515493, "executorUtilization": 0.95, "subTaskCount": 1, "waitingDurationMillis": 28481, "waitingTimeMillis": 28481}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#85", "duration": 11091251, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #85", "id": "85", "keepLog": false, "number": 85, "queueId": 257423, "result": "SUCCESS", "timestamp": 1741485937648, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/85/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 86, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/86/"}, "previousBuild": {"number": 84, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/84/"}}, "build_86": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 84", "upstreamBuild": 84, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 15456060, "executingTimeMillis": 15291324, "executorUtilization": 0.99, "subTaskCount": 1, "waitingDurationMillis": 26971, "waitingTimeMillis": 26971}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#86", "duration": 15456060, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #86", "id": "86", "keepLog": false, "number": 86, "queueId": 259423, "result": "SUCCESS", "timestamp": 1741626436851, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/86/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 87, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/87/"}, "previousBuild": {"number": 85, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/85/"}}, "build_87": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 85", "upstreamBuild": 85, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 32936867, "executingTimeMillis": 11683105, "executorUtilization": 0.35, "subTaskCount": 1, "waitingDurationMillis": 5263, "waitingTimeMillis": 5263}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#87", "duration": 32936867, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #87", "id": "87", "keepLog": false, "number": 87, "queueId": 263873, "result": "SUCCESS", "timestamp": 1741679801501, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/87/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 88, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/88/"}, "previousBuild": {"number": 86, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/86/"}}, "build_88": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 86", "upstreamBuild": 86, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 39886262, "executingTimeMillis": 11236820, "executorUtilization": 0.28, "subTaskCount": 1, "waitingDurationMillis": 7948, "waitingTimeMillis": 7948}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#88", "duration": 39886262, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #88", "id": "88", "keepLog": false, "number": 88, "queueId": 265767, "result": "SUCCESS", "timestamp": 1741691847185, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/88/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 89, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/89/"}, "previousBuild": {"number": 87, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/87/"}}, "build_89": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 87", "upstreamBuild": 87, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 41198282, "executingTimeMillis": 10527042, "executorUtilization": 0.26, "subTaskCount": 1, "waitingDurationMillis": 6357, "waitingTimeMillis": 6357}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#89", "duration": 41198282, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #89", "id": "89", "keepLog": false, "number": 89, "queueId": 266154, "result": "SUCCESS", "timestamp": 1741701062198, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/89/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 90, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/90/"}, "previousBuild": {"number": 88, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/88/"}}, "build_90": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 88", "upstreamBuild": 88, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 40958434, "executingTimeMillis": 11835139, "executorUtilization": 0.29, "subTaskCount": 1, "waitingDurationMillis": 5355, "waitingTimeMillis": 5355}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#90", "duration": 40958434, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #90", "id": "90", "keepLog": false, "number": 90, "queueId": 266924, "result": "SUCCESS", "timestamp": 1741720502024, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/90/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 91, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/91/"}, "previousBuild": {"number": 89, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/89/"}}, "build_91": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 89", "upstreamBuild": 89, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 29065734, "executingTimeMillis": 17237236, "executorUtilization": 0.59, "subTaskCount": 1, "waitingDurationMillis": 6641, "waitingTimeMillis": 6641}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#91", "duration": 29065734, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #91", "id": "91", "keepLog": false, "number": 91, "queueId": 268041, "result": "SUCCESS", "timestamp": 1741749631981, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/91/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 92, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/92/"}, "previousBuild": {"number": 90, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/90/"}}, "build_92": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 90", "upstreamBuild": 90, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 11245392, "executingTimeMillis": 11244711, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 8894, "waitingTimeMillis": 8894}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#92", "duration": 11245392, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #92", "id": "92", "keepLog": false, "number": 92, "queueId": 268287, "result": "SUCCESS", "timestamp": 1741787105304, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/92/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 93, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/93/"}, "previousBuild": {"number": 91, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/91/"}}, "build_93": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 91", "upstreamBuild": 91, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 11046847, "executingTimeMillis": 11046094, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 6669, "waitingTimeMillis": 6669}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#93", "duration": 11046847, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #93", "id": "93", "keepLog": false, "number": 93, "queueId": 268399, "result": "SUCCESS", "timestamp": 1741805940712, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/93/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 94, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/94/"}, "previousBuild": {"number": 92, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/92/"}}, "build_94": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 92", "upstreamBuild": 92, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 1, "buildingDurationMillis": 11841031, "executingTimeMillis": 11840290, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 8917, "waitingTimeMillis": 8917}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#94", "duration": 11841031, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #94", "id": "94", "keepLog": false, "number": 94, "queueId": 268736, "result": "SUCCESS", "timestamp": 1741855835587, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/94/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 95, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/95/"}, "previousBuild": {"number": 93, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/93/"}}, "build_95": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 94", "upstreamBuild": 94, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 1, "buildingDurationMillis": 10631496, "executingTimeMillis": 10630727, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 7469, "waitingTimeMillis": 7469}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#95", "duration": 10631496, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #95", "id": "95", "keepLog": false, "number": 95, "queueId": 268971, "result": "SUCCESS", "timestamp": 1741888840410, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/95/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 96, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/96/"}, "previousBuild": {"number": 94, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/94/"}}, "build_96": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 95", "upstreamBuild": 95, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 10589176, "executingTimeMillis": 10587958, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 7440, "waitingTimeMillis": 7440}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#96", "duration": 10589176, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #96", "id": "96", "keepLog": false, "number": 96, "queueId": 269309, "result": "SUCCESS", "timestamp": 1741939491829, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/96/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 97, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/97/"}, "previousBuild": {"number": 95, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/95/"}}, "build_97": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 96", "upstreamBuild": 96, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 11155744, "executingTimeMillis": 11154406, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 5652, "waitingTimeMillis": 5652}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#97", "duration": 11155744, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #97", "id": "97", "keepLog": false, "number": 97, "queueId": 269568, "result": "SUCCESS", "timestamp": 1741978087513, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/97/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 98, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/98/"}, "previousBuild": {"number": 96, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/96/"}}, "build_98": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 97", "upstreamBuild": 97, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 1, "buildingDurationMillis": 10455660, "executingTimeMillis": 10455004, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 6680, "waitingTimeMillis": 6680}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#98", "duration": 10455660, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #98", "id": "98", "keepLog": false, "number": 98, "queueId": 269850, "result": "SUCCESS", "timestamp": 1742026212249, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/98/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 99, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/99/"}, "previousBuild": {"number": 97, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/97/"}}, "build_99": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 98", "upstreamBuild": 98, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 10392549, "executingTimeMillis": 10391673, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 5357, "waitingTimeMillis": 5357}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#99", "duration": 10392549, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #99", "id": "99", "keepLog": false, "number": 99, "queueId": 270079, "result": "SUCCESS", "timestamp": 1742061581623, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/99/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 100, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/100/"}, "previousBuild": {"number": 98, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/98/"}}, "build_100": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 99", "upstreamBuild": 99, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 10323473, "executingTimeMillis": 10322789, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 6346, "waitingTimeMillis": 6346}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#100", "duration": 10323473, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #100", "id": "100", "keepLog": false, "number": 100, "queueId": 270368, "result": "SUCCESS", "timestamp": 1742111951179, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/100/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 101, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/101/"}, "previousBuild": {"number": 99, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/99/"}}, "build_101": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 100", "upstreamBuild": 100, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 10250972, "executingTimeMillis": 10250168, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 9436, "waitingTimeMillis": 9436}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#101", "duration": 10250972, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #101", "id": "101", "keepLog": false, "number": 101, "queueId": 270601, "result": "SUCCESS", "timestamp": 1742147985040, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/101/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 102, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/102/"}, "previousBuild": {"number": 100, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/100/"}}, "build_102": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 101", "upstreamBuild": 101, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 10196622, "executingTimeMillis": 10195880, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 8590, "waitingTimeMillis": 8590}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#102", "duration": 10196622, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #102", "id": "102", "keepLog": false, "number": 102, "queueId": 270914, "result": "SUCCESS", "timestamp": 1742198340714, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/102/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 103, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/103/"}, "previousBuild": {"number": 101, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/101/"}}, "build_103": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 104", "upstreamBuild": 104, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 9374994, "executingTimeMillis": 9371788, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 5335, "waitingTimeMillis": 5335}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#103", "duration": 9374994, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #103", "id": "103", "keepLog": false, "number": 103, "queueId": 271537, "result": "SUCCESS", "timestamp": 1742291555574, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/103/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 104, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/104/"}, "previousBuild": {"number": 102, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/102/"}}, "build_104": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 105", "upstreamBuild": 105, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 9276842, "executingTimeMillis": 9275975, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 8431, "waitingTimeMillis": 8431}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#104", "duration": 9276842, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #104", "id": "104", "keepLog": false, "number": 104, "queueId": 271730, "result": "SUCCESS", "timestamp": 1742319860174, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/104/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": {"number": 105, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/105/"}, "previousBuild": {"number": 103, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/103/"}}, "build_105": {"_class": "org.jenkinsci.plugins.workflow.job.WorkflowRun", "actions": [{"_class": "hudson.model.CauseAction", "causes": [{"_class": "hudson.model.Cause$UpstreamCause", "shortDescription": "Started by upstream project \"Compile_Chery/Compile_Mtk8678_Android_System_daily_build\" build number 106", "upstreamBuild": 106, "upstreamProject": "Compile_Chery/Compile_Mtk8678_Android_System_daily_build", "upstreamUrl": "job/Compile_Chery/job/Compile_Mtk8678_Android_System_daily_build/"}]}, {}, {"_class": "jenkins.metrics.impl.TimeInQueueAction", "blockedDurationMillis": 0, "blockedTimeMillis": 0, "buildableDurationMillis": 0, "buildableTimeMillis": 0, "buildingDurationMillis": 9327346, "executingTimeMillis": 9326639, "executorUtilization": 1.0, "subTaskCount": 1, "waitingDurationMillis": 9262, "waitingTimeMillis": 9262}, {"_class": "org.jenkinsci.plugins.workflow.libs.LibrariesAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.cps.EnvActionImpl"}, {}, {}, {}, {}, {}, {}, {}, {"_class": "org.jenkinsci.plugins.displayurlapi.actions.RunDisplayAction"}, {"_class": "org.jenkinsci.plugins.pipeline.modeldefinition.actions.RestartDeclarativePipelineAction"}, {}, {"_class": "org.jenkinsci.plugins.workflow.job.views.FlowGraphAction"}, {}, {}, {}, {}, {"_class": "org.marvelution.jji.export.ParentAction", "parent": {"name": "Compile_Mtk8678_Yocto_System_daily_build", "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/"}}, {}], "artifacts": [], "building": false, "description": null, "displayName": "#105", "duration": 9327346, "estimatedDuration": 9326394, "executor": null, "fullDisplayName": "Compile_Chery » Compile_Mtk8678_Yocto_System_daily_build #105", "id": "105", "keepLog": false, "number": 105, "queueId": 272058, "result": "SUCCESS", "timestamp": 1742370301090, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/105/", "changeSets": [], "culprits": [], "inProgress": false, "nextBuild": null, "previousBuild": {"number": 104, "url": "http://*************:8081/job/Compile_Chery/job/Compile_Mtk8678_Yocto_System_daily_build/104/"}}}