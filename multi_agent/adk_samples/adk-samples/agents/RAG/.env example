# Copy as .env file and fill your values below
# Run ./update_dotenv_example.sh to update .env-example from your .env file.

# Choose Model Backend: 0 -> ML Dev, 1 -> Vertex
GOOGLE_GENAI_USE_VERTEXAI=1

# M<PERSON> <PERSON> backend config
GOOGLE_API_KEY=YOUR_VALUE_HERE

# Vertex backend config
# Rag Engine only works with Vertex. So we should configure it to use Vertex:
GOOGLE_CLOUD_PROJECT=YOUR_VALUE_HERE
GOOGLE_CLOUD_LOCATION=YOUR_VALUE_HERE

# Existing corpus in Vertex RAG Engine to be used by RAG agent
# e.g. projects/123/locations/us-central1/ragCorpora/456
RAG_CORPUS=YOUR_VALUE_HERE 

# Staging bucket name for ADK agent deployment to Vertex AI Agent Engine (Shall respect this format gs://your-bucket-name)
STAGING_BUCKET=YOUR VALUE HERE

# Agent Engine ID in the following format: projects/<PROJECT_NUMBER>/locations/us-central1/reasoningEngines/<AGENT_ENGINE_ID>
AGENT_ENGINE_ID=YOUR VALUE HERE