# Choose Model Backend: 0 -> <PERSON><PERSON> <PERSON>, 1 -> Vertex
GOOGLE_GENAI_USE_VERTEXAI=1

# ML Dev backend config
# Visit https://aistudio.google.com to get this key
GOOGLE_API_KEY=

# Vertex backend config
GOOGLE_CLOUD_PROJECT=YOUR VALUE HERE
GOOGLE_CLOUD_LOCATION=YOUR VALUE HERE
MODEL="gemini-2.0-flash-001"

DATASET_ID="products_data_agent"
TABLE_ID="shoe_items"

# IMPORTANT: Setting this flag to 1 will disable web driver
DIS<PERSON>LE_WEB_DRIVER=0

# Staging bucket name for ADK agent deployment to Vertex AI Agent Engine (Do not include "gs://" for your bucket.)
STAGING_BUCKET=YOUR VALUE HERE
