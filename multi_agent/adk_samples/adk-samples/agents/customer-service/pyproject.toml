[tool.poetry]
name = "customer-service"
version = "0.1.0"
description = "customer service demo using Agent Development Kit"
authors = ["<PERSON><PERSON> <<EMAIL>>"]
license = "Apache License 2.0"
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
pydantic-settings = "^2.8.1"
tabulate = "^0.9.0"
cloudpickle = "^3.1.1"
pylint = "^3.3.6"
google-cloud-aiplatform = {extras = ["adk","agent_engine"], version = "^1.88.0"}

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
pytest-mock = "^3.14.0"
scikit-learn = "^1.6.1"
pytest-cov = "^6.0.0"
pytest-asyncio = "^0.25.3"
flake8-pyproject = "^1.2.3"
pylint = "^3.3.6"
pyink = "^24.10.1"
google-cloud-aiplatform = {extras = ["evaluation"], version = "^1.88.0"}


[tool.pytest.ini_options]
console_output_style = "progress"
addopts = "-vv -s"
#addopts = "-vv -s --pdb"
testpaths = ["tests/", "eval/"]
markers = [
    "unit"
]
log_level="ERROR"
log_cli=false
log_auto_indent=true
log_cli_date_format="%Y-%m-%d %H:%M:%S"
log_cli_format ="[%(asctime)s] %(levelname)s (%(funcName)s) \t [%(pathname)s:%(lineno)d] %(message)s"
filterwarnings = [
    "ignore::UserWarning",
]


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.mason"


[tool.pyink]
line-length=80
pyink-indentation=4
pyink-use-majority-quotes = true