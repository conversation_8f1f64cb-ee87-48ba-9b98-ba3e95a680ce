# Copy as .env file and fill your values below
# Run ./update_dotenv_example.sh to update .env-example from your .env file.

# Choose Model Backend: 0 -> ML Dev, 1 -> Vertex
GOOGLE_GENAI_USE_VERTEXAI=1

# ML <PERSON> backend config. Fill if using Ml Dev backend.
GOOGLE_API_KEY=YOUR_VALUE_HERE

# Vertex backend config
GOOGLE_CLOUD_PROJECT=YOUR_VALUE_HERE
GOOGLE_CLOUD_LOCATION=YOUR_VALUE_HERE

# SQLGen method 
NL2SQL_METHOD = "BASELINE" # BASELINE or CHASE

# Set up BigQuery Agent 
BQ_PROJECT_ID=YOUR_VALUE_HERE
BQ_DATASET_ID='forecasting_sticker_sales'

# Set up RAG Corpus for BQML Agent 
BQML_RAG_CORPUS_NAME=''              # Leave this empty as it will be populated automatically

# Set up Code Interpreter, if it exists. Else leave empty
CODE_INTERPRETER_EXTENSION_NAME=''    # Either '' or 'projects/{GOOGLE_CLOUD_PROJECT}/locations/us-central1/extensions/{EXTENSION_ID}' 

# Models used in Agents
ROOT_AGENT_MODEL='gemini-2.0-flash-001'
ANALYTICS_AGENT_MODEL='gemini-2.0-flash-001'
BIGQUERY_AGENT_MODEL='gemini-2.0-flash-001'
BASELINE_NL2SQL_MODEL='gemini-2.0-flash-001'
CHASE_NL2SQL_MODEL='gemini-2.0-flash-001'
BQML_AGENT_MODEL='gemini-2.0-flash-001'