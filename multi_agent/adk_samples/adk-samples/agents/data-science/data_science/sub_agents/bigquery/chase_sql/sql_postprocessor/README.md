# Support Post-processing of SQL after Agentic Generation.

This tool provides a way to postprocess the SQL generated by the agent.
Currently, it supports the following post-processing steps: 1. Translation of
the SQL from SQLite to BigQuery. 2. Correction of errors in the SQL before and
after translation.

## Usage

Currently, the post-processing is done within the `chase_db_tools.py` agent. To
use this agent, first configure the database agent to use Chase SQL.

Then, to use the postprocessing, set the `transpile_to_bigquery` argument to
`True` in the `chase_nl2sql` function. Optionally, you can also set the
`process_input_errors` and `process_tool_output_errors` arguments to `True` to
have the postprocessor correct errors in the SQL before and after translation.

### Current Defaults:

-   Model: gemini-2.0-flash-001
-   Temperature: 0.5
-   Number of candidates: 1
-   transpile_to_bigquery: True
-   process_input_errors: False
-   process_tool_output_errors: False
