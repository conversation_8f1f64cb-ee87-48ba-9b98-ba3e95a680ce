[tool.poetry]
name = "data-science"
version = "0.1"
description = "Data Science and Data QnA Multi-Agent"
authors = ["Meltem Subasioglu <<EMAIL>>"]
license = "Apache License 2.0"
readme = "README.md"
package-mode = true 


[tool.poetry.dependencies]
python = "^3.12"
python-dotenv = "^1.0.1"
google-adk = ">0.0.1"
immutabledict = "^4.2.1"
sqlglot = "^26.10.1"
db-dtypes = "^1.4.2"
regex = "^2024.11.6"
tabulate = "^0.9.0"
google-cloud-aiplatform = {extras = ["adk", "agent-engines"], version = "^1.88.0"}
absl-py = "^2.2.2"


[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"


[tool.pytest.ini_options]
console_output_style = "progress"
addopts = "-vv -s"
#addopts = "-vv -s --pdb"
testpaths = ["tests/"]
log_level="DEBUG"
log_cli=true
log_auto_indent=true
log_cli_date_format="%Y-%m-%d %H:%M:%S"
log_cli_format ="[%(asctime)s] %(levelname)s (%(funcName)s) \t [%(pathname)s:%(lineno)d] %(message)s"
filterwarnings = [
    #"error",
    "ignore::UserWarning",
    # note the use of single quote below to denote "raw" strings in TOML
    #'ignore:function ham\(\) is deprecated:DeprecationWarning',
]


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

