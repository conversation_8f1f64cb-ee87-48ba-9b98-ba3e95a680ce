# Copy as .env file and fill your values below

# Choose Model Backend: 0 -> <PERSON><PERSON> <PERSON>, 1 -> Vertex
GOOGLE_GENAI_USE_VERTEXAI=1

## M<PERSON> <PERSON> backend config
GOOGLE_API_KEY=YOUR_VALUE_HERE

## Vertex backend config
# Enter your GCP project id
GOOGLE_CLOUD_PROJECT=YOUR_VALUE_HERE
# Enter your GCP project number
GOOGLE_CLOUD_PROJECT_NUMBER=YOUR_VALUE_HERE
# Enter the GCP location for your project; for example "us-central1"
GOOGLE_CLOUD_LOCATION=YOUR_VALUE_HERE
# Choose a name for the dataset that will be created.
GOOGLE_CLOUD_BQ_DATASET=YOUR_VALUE_HERE
# Enter the name of an existing storage bucket to use.
GOOGLE_CLOUD_STORAGE_BUCKET=YOUR_VALUE_HERE
# Choose a model name; for example, "gemini-2.0-flash-001"
GOOGLE_GENAI_MODEL=YOUR_VALUE_HERE
# Timeseries codes used to retrieve the Fed futures timeseries from BigQuery.
# These codes are present in the sample dataset provided. If you add new
# timeseries, add the appropriate codes here.
GOOGLE_GENAI_FOMC_AGENT_TIMESERIES_CODES="SFRH5,SFRZ5"
GOOGLE_GENAI_FOMC_AGENT_LOG_LEVEL="INFO"
