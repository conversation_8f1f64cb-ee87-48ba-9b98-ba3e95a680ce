<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="892pt" height="444pt" viewBox="0.00 0.00 891.62 444.00">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 440)">
<polygon fill="white" stroke="none" points="-4,4 -4,-440 887.62,-440 887.62,4 -4,4"></polygon>
<!-- root_agent -->
<g id="node1" class="node">
<title>root_agent</title>
<ellipse fill="none" stroke="black" cx="64.4" cy="-127" rx="64.4" ry="18"></ellipse>
<text text-anchor="middle" x="64.4" y="-122.8" font-family="Times,serif" font-size="14.00">🤖 root_agent</text>
</g>
<!-- retrieve_meeting_data_agent -->
<g id="node2" class="node">
<title>retrieve_meeting_data_agent</title>
<ellipse fill="none" stroke="black" cx="299.99" cy="-364" rx="135.18" ry="18"></ellipse>
<text text-anchor="middle" x="299.99" y="-359.8" font-family="Times,serif" font-size="14.00">🤖 retrieve_meeting_data_agent</text>
</g>
<!-- root_agent&#45;&gt;retrieve_meeting_data_agent -->
<g id="edge3" class="edge">
<title>root_agent-&gt;retrieve_meeting_data_agent</title>
<path fill="none" stroke="black" d="M82.58,-144.43C125.57,-188.04 237.47,-301.58 281.15,-345.9"></path>
</g>
<!-- research_agent -->
<g id="node5" class="node">
<title>research_agent</title>
<ellipse fill="none" stroke="black" cx="299.99" cy="-191" rx="81.01" ry="18"></ellipse>
<text text-anchor="middle" x="299.99" y="-186.8" font-family="Times,serif" font-size="14.00">🤖 research_agent</text>
</g>
<!-- root_agent&#45;&gt;research_agent -->
<g id="edge10" class="edge">
<title>root_agent-&gt;research_agent</title>
<path fill="none" stroke="black" d="M111.55,-139.64C150.78,-150.39 207.02,-165.8 247.66,-176.94"></path>
</g>
<!-- store_state_tool -->
<g id="node7" class="node">
<title>store_state_tool</title>
<path fill="none" stroke="black" d="M871.62,-282C871.62,-282 773.47,-282 773.47,-282 767.47,-282 761.47,-276 761.47,-270 761.47,-270 761.47,-258 761.47,-258 761.47,-252 767.47,-246 773.47,-246 773.47,-246 871.62,-246 871.62,-246 877.62,-246 883.62,-252 883.62,-258 883.62,-258 883.62,-270 883.62,-270 883.62,-276 877.62,-282 871.62,-282"></path>
<text text-anchor="middle" x="822.55" y="-259.8" font-family="Times,serif" font-size="14.00">🔧 store_state_tool</text>
</g>
<!-- root_agent&#45;&gt;store_state_tool -->
<g id="edge12" class="edge">
<title>root_agent-&gt;store_state_tool</title>
<path fill="none" stroke="black" d="M116.51,-116.25C253.12,-88.64 623.36,-21.59 725.47,-83 785.69,-119.21 810.17,-207.28 818.22,-245.64"></path>
</g>
<!-- analysis_agent -->
<g id="node11" class="node">
<title>analysis_agent</title>
<ellipse fill="none" stroke="black" cx="299.99" cy="-18" rx="79.96" ry="18"></ellipse>
<text text-anchor="middle" x="299.99" y="-13.8" font-family="Times,serif" font-size="14.00">🤖 analysis_agent</text>
</g>
<!-- root_agent&#45;&gt;analysis_agent -->
<g id="edge11" class="edge">
<title>root_agent-&gt;analysis_agent</title>
<path fill="none" stroke="black" d="M83.22,-109.33C101.97,-91.72 133.11,-65.18 164.81,-50 185.87,-39.92 210.35,-32.87 232.59,-28.01"></path>
</g>
<!-- fetch_page_tool -->
<g id="node3" class="node">
<title>fetch_page_tool</title>
<path fill="none" stroke="black" d="M648.16,-436C648.16,-436 548.48,-436 548.48,-436 542.48,-436 536.48,-430 536.48,-424 536.48,-424 536.48,-412 536.48,-412 536.48,-406 542.48,-400 548.48,-400 548.48,-400 648.16,-400 648.16,-400 654.16,-400 660.16,-406 660.16,-412 660.16,-412 660.16,-424 660.16,-424 660.16,-430 654.16,-436 648.16,-436"></path>
<text text-anchor="middle" x="598.32" y="-413.8" font-family="Times,serif" font-size="14.00">🔧 fetch_page_tool</text>
</g>
<!-- retrieve_meeting_data_agent&#45;&gt;fetch_page_tool -->
<g id="edge1" class="edge">
<title>retrieve_meeting_data_agent-&gt;fetch_page_tool</title>
<path fill="none" stroke="black" d="M381.57,-378.68C430.25,-387.55 491.3,-398.68 536.03,-406.83"></path>
</g>
<!-- extract_page_data_agent -->
<g id="node4" class="node">
<title>extract_page_data_agent</title>
<path fill="none" stroke="black" d="M672.64,-382C672.64,-382 524,-382 524,-382 518,-382 512,-376 512,-370 512,-370 512,-358 512,-358 512,-352 518,-346 524,-346 524,-346 672.64,-346 672.64,-346 678.64,-346 684.64,-352 684.64,-358 684.64,-358 684.64,-370 684.64,-370 684.64,-376 678.64,-382 672.64,-382"></path>
<text text-anchor="middle" x="598.32" y="-359.8" font-family="Times,serif" font-size="14.00">🤖 extract_page_data_agent</text>
</g>
<!-- retrieve_meeting_data_agent&#45;&gt;extract_page_data_agent -->
<g id="edge2" class="edge">
<title>retrieve_meeting_data_agent-&gt;extract_page_data_agent</title>
<path fill="none" stroke="black" d="M435.28,-364C461.32,-364 487.93,-364 511.86,-364"></path>
</g>
<!-- summarize_meeting_agent -->
<g id="node6" class="node">
<title>summarize_meeting_agent</title>
<ellipse fill="none" stroke="black" cx="598.32" cy="-310" rx="127.15" ry="18"></ellipse>
<text text-anchor="middle" x="598.32" y="-305.8" font-family="Times,serif" font-size="14.00">🤖 summarize_meeting_agent</text>
</g>
<!-- research_agent&#45;&gt;summarize_meeting_agent -->
<g id="edge5" class="edge">
<title>research_agent-&gt;summarize_meeting_agent</title>
<path fill="none" stroke="black" d="M329.59,-208.15C362.7,-227.39 419.32,-258.45 471.17,-278 488.51,-284.54 507.68,-290.17 525.74,-294.8"></path>
</g>
<!-- research_agent&#45;&gt;store_state_tool -->
<g id="edge6" class="edge">
<title>research_agent-&gt;store_state_tool</title>
<path fill="none" stroke="black" d="M341.17,-206.94C375.2,-219.65 425.58,-236.67 471.17,-245 571.7,-263.37 690.92,-265.82 761.18,-265.33"></path>
</g>
<!-- compare_statements_tool -->
<g id="node8" class="node">
<title>compare_statements_tool</title>
<path fill="none" stroke="black" d="M674.61,-236C674.61,-236 522.04,-236 522.04,-236 516.04,-236 510.04,-230 510.04,-224 510.04,-224 510.04,-212 510.04,-212 510.04,-206 516.04,-200 522.04,-200 522.04,-200 674.61,-200 674.61,-200 680.61,-200 686.61,-206 686.61,-212 686.61,-212 686.61,-224 686.61,-224 686.61,-230 680.61,-236 674.61,-236"></path>
<text text-anchor="middle" x="598.32" y="-213.8" font-family="Times,serif" font-size="14.00">🔧 compare_statements_tool</text>
</g>
<!-- research_agent&#45;&gt;compare_statements_tool -->
<g id="edge7" class="edge">
<title>research_agent-&gt;compare_statements_tool</title>
<path fill="none" stroke="black" d="M375.77,-197.81C416.49,-201.52 467.1,-206.14 509.67,-210.01"></path>
</g>
<!-- fetch_transcript_tool -->
<g id="node9" class="node">
<title>fetch_transcript_tool</title>
<path fill="none" stroke="black" d="M661.39,-182C661.39,-182 535.26,-182 535.26,-182 529.26,-182 523.26,-176 523.26,-170 523.26,-170 523.26,-158 523.26,-158 523.26,-152 529.26,-146 535.26,-146 535.26,-146 661.39,-146 661.39,-146 667.39,-146 673.39,-152 673.39,-158 673.39,-158 673.39,-170 673.39,-170 673.39,-176 667.39,-182 661.39,-182"></path>
<text text-anchor="middle" x="598.32" y="-159.8" font-family="Times,serif" font-size="14.00">🔧 fetch_transcript_tool</text>
</g>
<!-- research_agent&#45;&gt;fetch_transcript_tool -->
<g id="edge8" class="edge">
<title>research_agent-&gt;fetch_transcript_tool</title>
<path fill="none" stroke="black" d="M375.77,-184.19C420.77,-180.09 477.87,-174.88 522.83,-170.79"></path>
</g>
<!-- compute_rate_move_probability_tool -->
<g id="node10" class="node">
<title>compute_rate_move_probability_tool</title>
<path fill="none" stroke="black" d="M708.82,-128C708.82,-128 487.82,-128 487.82,-128 481.82,-128 475.82,-122 475.82,-116 475.82,-116 475.82,-104 475.82,-104 475.82,-98 481.82,-92 487.82,-92 487.82,-92 708.82,-92 708.82,-92 714.82,-92 720.82,-98 720.82,-104 720.82,-104 720.82,-116 720.82,-116 720.82,-122 714.82,-128 708.82,-128"></path>
<text text-anchor="middle" x="598.32" y="-105.8" font-family="Times,serif" font-size="14.00">🔧 compute_rate_move_probability_tool</text>
</g>
<!-- research_agent&#45;&gt;compute_rate_move_probability_tool -->
<g id="edge9" class="edge">
<title>research_agent-&gt;compute_rate_move_probability_tool</title>
<path fill="none" stroke="black" d="M345.08,-175.72C379.23,-164.2 427.87,-148.47 471.17,-137 482.43,-134.02 494.32,-131.13 506.12,-128.43"></path>
</g>
<!-- summarize_meeting_agent&#45;&gt;store_state_tool -->
<g id="edge4" class="edge">
<title>summarize_meeting_agent-&gt;store_state_tool</title>
<path fill="none" stroke="black" d="M672.09,-294.94C700.95,-288.96 733.74,-282.18 761.24,-276.48"></path>
</g>
</g>
</svg>
