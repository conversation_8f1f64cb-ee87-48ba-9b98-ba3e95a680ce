[tool.poetry]
name = "fomc-research"
version = "0.1"
license = "Apache-2.0"
description = "FOMC Research Agent Demo using Google ADK"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.9"
absl-py = "^2.2.1"
diff-match-patch = "^20241021"
google-adk = ">=0.0.2"
google-cloud-bigquery = "^3.30.0"
google-genai = "^1.5.0"
pdfplumber = "^0.11.5"
pydantic = "^2.10.6"
requests = "^2.32.3"
tabulate = "^0.9.0"
scikit-learn = "^1.6.1"
google-cloud-aiplatform = {extras = ["adk", "agent-engines"], version = "^1.88.0"}


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
