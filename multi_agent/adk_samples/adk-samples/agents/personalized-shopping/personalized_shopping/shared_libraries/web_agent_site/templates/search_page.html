<!DOCTYPE html>
<!--
 Copyright 2025 Google LLC

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<html>
  <head>
    <link rel="stylesheet" href="{{url_for('static', filename='style.css')}}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <link rel="icon" href="data:,">
    <style>
      /* Make sure the HTML and body elements can scroll */
      html, body {
        height: 100%;  /* Ensure the body takes up the full height */
        margin: 0;  /* Remove any default margin */
        padding: 0;  /* Remove any default padding */
        overflow: auto;  /* Allow scrolling when content overflows */
      }

      /* Optional: Add some padding to prevent elements from being too close to the edge */
      .container {
        padding-bottom: 50px;  /* Add padding at the bottom if needed */
      }

      .top-buffer {
        margin-top: 20px; /* Add some spacing between rows */
      }
    </style>
  </head>
  <body>
    <!-- Code reference: https://bootsnipp.com/snippets/m13mN -->
    <div class="container" style="margin-top: 8%;">
      <div class="col-md-6 col-md-offset-3">     
        <div class="row">
          <div id="logo" class="text-center">
            <h2>WebShop</h2>
          </div>
          <div id="instruction-text" class="text-center">
            <h4>Instruction: <br>{{ instruction_text }}</h4>
          </div>
          <form role="form" id="form-buscar" method="post" action="{{url_for('index', session_id=session_id)}}">
            <div class="form-group">
              <div class="input-group">
                <input id="search_input" class="form-control" type="text" name="search_query" placeholder="Search..." required/>
                <span class="input-group-btn">
                  <button class="btn btn-success" type="submit"><i class="glyphicon glyphicon-search" aria-hidden="true"></i>Search</button>
                </span>
              </div>
            </div>
          </form>
        </div>            
      </div>
    </div>
  </body>
</html>