[project]
name = "personalized-shopping"
version = "0.1.0"
description = "Personalzied Shopping with Google ADK"
authors = [{ name = "<PERSON><PERSON>", email = "<EMAIL>" }]
license = "Apache License 2.0"
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
google-cloud-aiplatform = {extras = ["adk", "agent-engines"], version = "^1.88.0"}
google-genai = "^1.9.0"
google-adk = ">0.0.1"
pydantic = "^2.10.6"
python-dotenv = "^1.0.1"
pyserini = "^0.43.0"
rich = "^13.9.4"
cleantext = "^1.1.4"
Flask = "^3.1.0"
spacy = "^3.8.2"
en_core_web_sm = { url = "https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.8.0/en_core_web_sm-3.8.0-py3-none-any.whl" }
thefuzz = "^0.22.1"
gym = "0.24.0"
torch = "^2.5.1"
torchvision = "^0.20.1"
gdown = "^5.2.0"
pytest = "^8.3.5"
tabulate = "^0.9.0"
absl-py = "^2.2.1"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"