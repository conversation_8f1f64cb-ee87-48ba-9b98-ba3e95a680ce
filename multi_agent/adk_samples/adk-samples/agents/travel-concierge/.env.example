# Choose Model Backend: 0 -> <PERSON><PERSON> <PERSON>, 1 -> Vertex
GOOGLE_GENAI_USE_VERTEXAI=YOUR_VALUE_HERE
# ML Dev backend config, ignore if using Vertex.
GOOGLE_API_KEY=YOUR_VALUE_HERE

# Vertex backend config
GOOGLE_CLOUD_PROJECT=YOUR_VALUE_HERE
GOOGLE_CLOUD_LOCATION=YOUR_VALUE_HERE

# Places API
GOOGLE_PLACES_API_KEY=YOUR_API_KEY_HERE

# GCS Storage Bucket name - for Agent Engine deployment test
GOOGLE_CLOUD_STORAGE_BUCKET=YOUR_BUCKET_NAME_HERE

# Sample Scenario Path - Default is an empty itinerary
# This will be loaded upon first user interaction.
#
# Uncomment one of the two, or create your own.
#
# TRAVEL_CONCIERGE_SCENARIO=eval/itinerary_seattle_example.json
TRAVEL_CONCIERGE_SCENARIO=eval/itinerary_empty_default.json