[project]
name = "travel-concierge"
version = "0.1.0"
description = "Orchestrates personalized travel experiences, illustrates means to integrate booking systems, and provides support throughout the traveler's journey, from initial planning, booking, to real-time itinerary services and alerts."
authors = [
    {name = "<PERSON>", email = "<EMAIL>"},
    {name = "<PERSON><PERSON>", email = "as<PERSON><PERSON>@google.com"},
    {name = "<PERSON>", email = "x<PERSON><PERSON><PERSON><PERSON>@google.com"},
    {name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>"},
    {name = "<PERSON>", email = "<EMAIL>"}
]
license = "Apache License 2.0"
readme = "README.md"
requires-python = ">=3.11"

[tool.poetry.dependencies]
google-cloud-aiplatform = { extras = [
    "agent_engines",
    "adk",
], git = "https://github.com/googleapis/python-aiplatform.git", rev = "copybara_738852226" }
python = "^3.11"
pydantic = "^2.10.6"
python-dotenv = "^1.0.1"
google-genai = "^1.9.0"
google-adk = ">=0.0.2"

[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
google-adk = { version = ">=0.0.2", extras = ["eval"] }

[tool.poetry.group.deployment]
optional = true

[tool.poetry.group.deployment.dependencies]
absl-py = "^2.2.1"
cloudpickle = "^3.1.1"
flake8-pyproject = "^1.2.3"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"