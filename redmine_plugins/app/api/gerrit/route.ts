import clientPromise from "@/db/mongodb";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const client = await clientPromise;
    const db = client.db("Redmine_Plugin");
    const collection = db.collection("gerrit");
    const issue_ids = body.issue_ids;
    const currentTime = new Date().getTime();
    const issues = await prisma.issues.findMany({
      where: {
        id: {
          in: body.issue_ids,
        },
      },
    });
    const result = await collection.insertOne({
      ...body,
      timestamp: currentTime,
      issues,
    });

    delete body.issue_ids;

    const brief_deploy = db.collection("brief_gerrit");
    // 构建 bulkWrite 操作
    const bulkOps = issue_ids.map((issue_id: number) => ({
      updateOne: {
        filter: { issue_id: issue_id },
        update: {
          $set: {
            issue_id: issue_id,
            timestamp: currentTime,
            ...issues.find((issue) => issue.id === issue_id),
            ...body,
            project_id: +body.project_id,
          },
          $push: {
            deploys: {
              ...body,
              timestamp: currentTime,
            },
          },
        },
        upsert: true,
      },
    }));

    // 执行 bulkWrite 操作
    const brief_result = await brief_deploy.bulkWrite(bulkOps);

    return Response.json({
      success: true,
      insertedId: result.insertedId,
      brief_result,
    });
  } catch (error) {
    console.log(error);
    return Response.json({
      error,
      success: false,
    });
  }
}
