"use client";
import { Flex, Menu } from "antd";
import Sider from "antd/es/layout/Sider";
import { ItemType, MenuItemType } from "antd/es/menu/interface";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import React from "react";

type Props = {
  items: ItemType<MenuItemType>[];
};

function SiderMenu({ items }: Props) {
  const searchParams = useSearchParams();
  const component = searchParams?.get("component") || "dashboard";
  const router = useRouter();
  return (
    <Sider collapsible theme="light" className="shadow-md">
      <Flex justify="center" className="my-4">
        <Image src={"/next.svg"} alt="Logo" width={120} height={30} />
      </Flex>
      <Menu
        items={items}
        style={{ border: "none" }}
        defaultSelectedKeys={[component]}
        onClick={(info) => {
          router.push(`?component=${info.key}`);
        }}
      />
    </Sider>
  );
}

export default SiderMenu;
