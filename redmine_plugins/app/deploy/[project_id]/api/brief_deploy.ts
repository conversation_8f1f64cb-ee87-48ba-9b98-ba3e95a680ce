"use server";

import clientPromise from "@/db/mongodb";

type GetBriefDeployList = (params: {
  project_id: number;
  page: number;
  pageSize: number;
  issue_id: string | null;
}) => Promise<any>;

export const getBriefDeployList: GetBriefDeployList = async ({
  project_id,
  page,
  pageSize,
  issue_id,
}) => {
  try {
    const client = await clientPromise;
    const db = client.db("Redmine_Plugin");
    const collection = db.collection("brief_deploy");
    const query: any = { project_id };
    if (issue_id) {
      query.issue_id = +issue_id;
    }
    const list_with_ids = await collection
      .find(query)
      .sort({ timestamp: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    const list = list_with_ids.map((item) => ({
      ...item,
      _id: item._id.toString(),
    }));

    return {
      success: true,
      total: await collection.countDocuments(query),
      list,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      error: error,
    };
  }
};

export const getBriefGerritList: GetBriefDeployList = async ({
  project_id,
  page,
  pageSize,
  issue_id,
}) => {
  try {
    const client = await clientPromise;
    const db = client.db("Redmine_Plugin");
    const collection = db.collection("brief_gerrit");
    const query: any = { project_id };
    if (issue_id) {
      query.issue_id = +issue_id;
    }
    const list_with_ids = await collection
      .find(query)
      .sort({ timestamp: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    const list = list_with_ids.map((item) => ({
      ...item,
      _id: item._id.toString(),
    }));

    return {
      success: true,
      total: await collection.countDocuments(query),
      list,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      error: error,
    };
  }
};

export const getEventDeployList: GetBriefDeployList = async ({
  project_id,
  page,
  pageSize,
  issue_id,
}) => {
  try {
    const client = await clientPromise;
    const db = client.db("Redmine_Plugin");
    const collection = db.collection("deploy");
    const query: any = { project_id: project_id.toString() };
    if (issue_id) {
      query.issue_ids = { $in: [+issue_id] };
    }
    const list_with_ids = await collection
      .find(query)
      .sort({ timestamp: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    const list = list_with_ids.map((item) => ({
      ...item,
      _id: item._id.toString(),
    }));

    return {
      success: true,
      total: await collection.countDocuments(query),
      list,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      error: error,
    };
  }
};

export const getEventGerritList: GetBriefDeployList = async ({
  project_id,
  page,
  pageSize,
  issue_id,
}) => {
  try {
    const client = await clientPromise;
    const db = client.db("Redmine_Plugin");
    const collection = db.collection("gerrit");
    const query: any = { project_id: project_id.toString() };
    if (issue_id) {
      query.issue_ids = { $in: [+issue_id] };
    }
    const list_with_ids = await collection
      .find(query)
      .sort({ timestamp: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    const list = list_with_ids.map((item) => ({
      ...item,
      _id: item._id.toString(),
    }));

    return {
      success: true,
      total: await collection.countDocuments(query),
      list,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      error: error,
    };
  }
};
