"use server";

import { PrismaClient } from "@prisma/client";
const prisma = new PrismaClient();

export const getProjectInfo = async (projectId: number) => {
  try {
    const project = await prisma.projects.findUnique({
      where: {
        id: projectId,
      },
    });

    if (!project) {
      return {
        success: false,
        error: "Project not found",
      };
    } else {
      return {
        success: true,
        project: project,
      };
    }
  } catch (error) {
    console.log(error);
    return {
      success: false,
      error: error,
    };
  }
};

export const getProjectIssues = async ({
  project_id,
}: {
  project_id: number;
}) => {
  try {
    const issues = await prisma.issues.findMany({
      where: {
        project_id: project_id,
      },
    });

    const authorIds = issues
      .filter((issue) => issue.assigned_to_id !== null)
      .map((issue) => issue.assigned_to_id || 0);

    const users = await prisma.users.findMany({
      where: {
        id: {
          in: authorIds,
        },
      },
      select: {
        id: true,
        firstname: true,
        lastname: true,
      },
    });

    if (!users || !issues) {
      return {
        success: false,
        error: "Issues not found",
      };
    } else {
      return {
        success: true,
        issues,
        users,
      };
    }
  } catch (error) {
    console.log(error);
  }
};
