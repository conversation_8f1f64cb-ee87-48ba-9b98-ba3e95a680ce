"use client";
import {
  CiCircleOutlined,
  HomeOutlined,
  ProjectOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { Breadcrumb } from "antd";
import { useSearchParams } from "next/navigation";
import React from "react";

type Props = {
  project: any;
};

function BriefInfo({ project }: Props) {
  const searchParams = useSearchParams();
  const component = searchParams.get("component");
  return (
    <div className="bg-white shadow-sm p-4 mb-2">
      <Breadcrumb
        items={[
          {
            href: "",
            title: <HomeOutlined />,
          },
          {
            href: "",
            title: (
              <>
                <ProjectOutlined />
                <span>{project.name}</span>
              </>
            ),
          },
          {
            title: (
              <>
                <CiCircleOutlined />
                <span>{component}</span>
              </>
            ),
          },
        ]}
      />
      <h2 className="text-xl font-bold mt-2">{project.name} 部署图</h2>
      <p className="text-gray-500 mt-2">
        {project.description || "这个项目没有项目介绍"}
      </p>
    </div>
  );
}

export default BriefInfo;
