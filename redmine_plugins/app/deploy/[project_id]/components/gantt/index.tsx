"use client";
import React, { useRef, useEffect, useMemo } from "react";
import {
  DataGroupCollectionType,
  DataItemCollectionType,
  DataSet,
  Timeline,
  TimelineOptions,
} from "vis-timeline/standalone/esm/vis-timeline-graph2d";
import moment from "moment";
import useSWR from "swr";
import { getProjectIssues } from "../../api/redmine";
import { useParams, useRouter } from "next/navigation";

const VisTimeline = () => {
  const timelineContainer = useRef<HTMLDivElement>(null!);
  const params = useParams();
  const project_id = params.project_id as string;
  const { data } = useSWR({ project_id: +project_id }, async (params) => {
    const result = await getProjectIssues(params);
    return result;
  });
  const router = useRouter();

  const groups = useMemo<DataGroupCollectionType>(() => {
    if (data?.success) {
      return (
        data.users?.map((user) => ({
          id: user.id,
          content: (user.firstname || "") + (user.lastname || ""),
        })) || []
      );
    } else {
      return [];
    }
  }, [data?.success, data?.users]);

  const items = useMemo<DataItemCollectionType>(() => {
    if (data?.success) {
      return data.issues!.map((issue) => ({
        id: issue.id,
        content: issue.subject || "",
        start: moment(issue.start_date).format("YYYY-MM-DD"),
        end: moment(issue.due_date).format("YYYY-MM-DD"),
        group: issue.assigned_to_id,
        title: issue.subject || "",
      }));
    } else {
      return [];
    }
  }, [data?.issues, data?.success]);

  useEffect(() => {
    const options: TimelineOptions = {
      width: "100%",
      height: "100%",
      stack: true,
      showCurrentTime: true,
      locale: "en",
      horizontalScroll: true,
      zoomKey: "ctrlKey",
      orientation: "top",
      zoomMin: 1000 * 60 * 60 * 24, // one day in milliseconds
      zoomMax: 1000 * 60 * 60 * 24 * 30 * 12, // about one month in milliseconds
      // template: createRootForTimelineItem,
      // timeAxis: {
      //   scale: "day",
      //   step: 1,
      // },
      format: {
        minorLabels: {
          day: "ddd D", // 显示周几
          weekday: "ddd",
          week: "w",
          month: "MMM",
          year: "YYYY",
        },
        majorLabels: {
          millisecond: "HH:mm:ss",
          second: "D MMMM HH:mm",
          minute: "ddd D MMMM",
          hour: "ddd D MMMM",
          weekday: "MMMM YYYY",
          day: "MMMM YYYY",
          week: "MMMM YYYY",
          month: "YYYY",
          year: "",
        },
      },
      // editable: {
      //   add: false,
      //   updateTime: true,
      //   updateGroup: false,
      //   remove: false,
      // },
      showWeekScale: true,
      groupHeightMode: "fixed",
      start: moment().format("YYYY-MM-DD"),
      end: moment().add(1, "month").format("YYYY-MM-DD"),
      moveable: true,
      multiselect: true,
      // configure: true,
      // itemsAlwaysDraggable: {
      //   item: true,
      //   range: true,
      // },
    };
    // @ts-ignore
    const timeline = new Timeline(
      timelineContainer.current!,
      items,
      groups,
      options
    );

    timeline.on("doubleClick", (props) => {
      if (props.what === "item") {
        window.open(
          "https://redmine.bitech-auto.com/redmine/issues/" + props.item,
          "_blank"
        );
      }
    });

    return () => {
      timeline.destroy();
    };
  }, [groups, items]);

  return (
    <div
      ref={timelineContainer}
      style={{
        height: "calc(100% - 138px)",
        background: "#fff",
        borderRadius: "8px",
      }}
    />
  );
};

export default VisTimeline;
