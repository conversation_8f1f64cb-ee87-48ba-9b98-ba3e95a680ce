"use client";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  DatePicker,
  Flex,
  Form,
  Input,
  Row,
  Select,
} from "antd";
import React from "react";

function SearchArea() {
  return (
    <Card style={{ margin: "8px" }}>
      <Form labelCol={{ span: 4 }}>
        <Row gutter={24}>
          <Col xl={{ span: 7 }} md={{ span: 11 }}>
            <Form.Item label="环境类型">
              <Select className="w-full!" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col xl={{ span: 7 }} md={{ span: 11 }}>
            <Form.Item label="环境名称">
              <Input className="w-full!" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col xl={{ span: 7 }} md={{ span: 11 }}>
            <Form.Item label="时间段">
              <DatePicker.RangePicker style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col xl={{ span: 3 }} md={{ span: 24 }}>
            <Flex justify="end">
              <Button className="mr-2">重置</Button>
              <Button type="primary">查询</Button>
            </Flex>
          </Col>
        </Row>
      </Form>
    </Card>
  );
}

export default SearchArea;
