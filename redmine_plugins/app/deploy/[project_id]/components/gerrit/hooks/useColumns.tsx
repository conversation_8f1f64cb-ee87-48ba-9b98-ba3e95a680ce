"use client";
import { ColumnType } from "antd/es/table";
import React from "react";
import {
  CheckCircleFilled,
  CloseCircleFilled,
  SnippetsOutlined,
  GithubOutlined,
  CopyOutlined,
  BranchesOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { Flex, Button, Tag, Modal, Table, Typography } from "antd";
import dayjs from "dayjs";
import useEventColumns from "./useEventColumns";

function useColumns(): ColumnType<any>[] {
  const columns = useEventColumns();
  return [
    {
      title: "事务名称",
      key: "subject",
      ellipsis: true,
      dataIndex: "subject",
      width: 500,
      fixed: "left",
      render: (text: string, record: any) => (
        <Flex justify="start" align="center">
          <Button
            href={`https://redmine.bitech-auto.com/redmine/issues/${record.id}`}
            type={"link"}
            icon={<SnippetsOutlined />}
            target="_blank"
          >
            {text}
          </Button>
        </Flex>
      ),
    },
    {
      title: "ChangeNumber",
      dataIndex: "change_number",
      key: "change_number",
      width: 150,
      render(text, record: any) {
        return (
          <Button
            type="link"
            target="_blank"
            href={record.change_url}
            icon={<GithubOutlined />}
          >
            #{text}
          </Button>
        );
      },
    },
    {
      title: "仓库",
      key: "change_url",
      dataIndex: "change_url",
      width: 200,
      ellipsis: true,
      render(text) {
        const repo = text
          .replace("http://gerrit.bitech-auto.com/c/", "")
          .split("/+")[0];
        return (
          <>
            <Typography.Text
              copyable={{
                text: repo,
              }}
            />
            <span className="ml-1">{repo}</span>
          </>
        );
      },
    },
    {
      title: "分支",
      key: "branch",
      dataIndex: "branch",
      width: 150,
      render(text) {
        return (
          <Tag color="green" icon={<BranchesOutlined />}>
            {text}
          </Tag>
        );
      },
    },
    {
      title: "提交人",
      key: "committer",
      dataIndex: "committer",
      width: 150,
      ellipsis: true,
      render(text) {
        return (
          <Tag color="blue" icon={<UserOutlined />}>
            {text}
          </Tag>
        );
      },
    },
    {
      title: "CommitId",
      dataIndex: "commit_id",
      key: "commit_id",
      width: 150,
      ellipsis: true,
      render(text, record: any) {
        return (
          <>
            <Typography.Text
              copyable={{
                text,
              }}
            />
            <span className="ml-1">{text}</span>
          </>
        );
      },
    },
    {
      title: "CommitMessage",
      dataIndex: "commit_message",
      key: "commit_message",
      width: 300,
      ellipsis: true,
    },

    {
      title: "最近部署时间",
      key: "timestamp",
      width: 200,
      dataIndex: "timestamp",
      render: (text: string, record: any) =>
        dayjs(text).format("YYYY-MM-DD HH:mm:ss"),
    },
    {
      title: "操作",
      key: "action",
      width: 180,
      fixed: "right",
      render: (text: string, record: any) => (
        <Flex justify="space-around" align="center">
          <Button
            type="link"
            size="small"
            onClick={() => {
              Modal.info({
                title: "提交历史",
                closable: true,
                footer: null,
                width: "80%",
                okCancel: false,
                content: (
                  <Table
                    style={{ marginTop: "16px" }}
                    size={"small"}
                    pagination={{
                      showLessItems: true,
                    }}
                    bordered
                    scroll={{ x: 800 }}
                    dataSource={record.deploys}
                    rowKey={(record) => record.id}
                    columns={columns}
                  />
                ),
              });
            }}
          >
            查看所有提交
          </Button>
        </Flex>
      ),
    },
  ];
}

export default useColumns;
