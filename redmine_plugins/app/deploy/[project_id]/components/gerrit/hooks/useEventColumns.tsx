import {
  GithubOutlined,
  BranchesOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { Tag, Button, Typography } from "antd";
import dayjs from "dayjs";
import React from "react";

function useEventColumns() {
  return [
    {
      title: "最近部署时间",
      key: "timestamp",
      width: 200,
      dataIndex: "timestamp",
      render: (text: string, record: any) =>
        text ? dayjs(text).format("YYYY-MM-DD HH:mm:ss"): "N.A",
    },
    {
      title: "ChangeNumber",
      dataIndex: "change_number",
      key: "change_number",
      width: 150,
      render(text: string, record: any) {
        return (
          <Button
            type="link"
            target="_blank"
            href={record.change_url}
            icon={<GithubOutlined />}
          >
            #{text}
          </Button>
        );
      },
    },
    {
      title: "仓库",
      key: "change_url",
      dataIndex: "change_url",
      width: 200,
      ellipsis: true,
      render(text: string) {
        const repo = text
          .replace("http://gerrit.bitech-auto.com/c/", "")
          .split("/+")[0];
        return (
          <>
            <Typography.Text
              copyable={{
                text: repo,
              }}
            />
            <span className="ml-1">{repo}</span>
          </>
        );
      },
    },
    {
      title: "分支",
      key: "branch",
      dataIndex: "branch",
      width: 150,
      render(text: string) {
        return (
          <Tag color="green" icon={<BranchesOutlined />}>
            {text}
          </Tag>
        );
      },
    },
    {
      title: "提交人",
      key: "committer",
      dataIndex: "committer",
      width: 150,
      ellipsis: true,
      render(text: string) {
        return (
          <Tag color="blue" icon={<UserOutlined />}>
            {text}
          </Tag>
        );
      },
    },
    {
      title: "CommitId",
      dataIndex: "commit_id",
      key: "commit_id",
      width: 150,
      ellipsis: true,
      render(text: string, record: any) {
        return (
          <>
            <Typography.Text
              copyable={{
                text,
              }}
            />
            <span className="ml-1">{text}</span>
          </>
        );
      },
    },
    {
      title: "CommitMessage",
      dataIndex: "commit_message",
      key: "commit_message",
      width: 300,
      ellipsis: true,
    },
  ];
}

export default useEventColumns;
