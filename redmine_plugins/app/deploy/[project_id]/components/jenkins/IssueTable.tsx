"use client";
import { Card, Result, Table } from "antd";
import React, { useMemo } from "react";
import useS<PERSON> from "swr";
import { getBriefDeployList } from "../../api/brief_deploy";
import { useParams, useSearchParams } from "next/navigation";
import useColumns from "./hooks/useColumns";

function IssueTable() {
  const [pagination, setPagination] = React.useState<{
    page: number;
    pageSize: number;
  }>({
    page: 1,
    pageSize: 10,
  });
  const searchParams = useSearchParams();
  const issue_id = searchParams.get("issue_id");

  const columns = useColumns();
  const params = useParams();
  const { data, error } = useSWR(
    {
      ...pagination,
      project_id: +(params.project_id as string),
      issue_id,
      api: "jenkins_issue",
    },
    async (params) => {
      // @ts-ignore
      delete params.api;
      return await getBriefDeployList(params);
    }
  );

  const dataSource = useMemo(() => {
    if (data) {
      return data.list || [];
    } else {
      return [];
    }
  }, [data]);

  if (error) {
    return (
      <Result status={500} title={"500"} subTitle={JSON.stringify(error)} />
    );
  } else {
    return (
      <Table
        size="small"
        bordered
        dataSource={dataSource}
        columns={columns}
        style={{
          scrollbarWidth: "thin",
        }}
        scroll={{ x: 1200 }}
        pagination={{
          ...pagination,
          total: data?.total || 0,
          showPrevNextJumpers: true,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} issues`,
          onChange: (page, pageSize) => {
            setPagination({ page, pageSize });
          },
        }}
      />
    );
  }
}

export default IssueTable;
