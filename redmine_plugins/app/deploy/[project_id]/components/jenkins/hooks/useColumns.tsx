"use client";
import { ColumnType } from "antd/es/table";
import React from "react";
import {
  CheckCircleFilled,
  CloseCircleFilled,
  SnippetsOutlined,
} from "@ant-design/icons";
import { Flex, Button, Tag, Modal, Table } from "antd";
import dayjs from "dayjs";
import useEventColumns from "./useEventColumns";

function useColumns(): ColumnType<any>[] {
  const columns = useEventColumns();
  return [
    {
      title: "事务名称",
      key: "subject",
      ellipsis: true,
      dataIndex: "subject",
      width: 500,
      fixed: "left",
      render: (text: string, record: any) => (
        <Flex justify="start" align="center">
          <Button
            href={`https://redmine.bitech-auto.com/redmine/issues/${record.id}`}
            type={"link"}
            icon={<SnippetsOutlined />}
            target="_blank"
          >
            {text}
          </Button>
        </Flex>
      ),
    },
    {
      title: "最近部署时间",
      key: "timestamp",
      width: 200,
      dataIndex: "timestamp",
      render: (text: string, record: any) =>
        dayjs(text).format("YYYY-MM-DD HH:mm:ss"),
    },
    {
      title: "最近部署结果",
      dataIndex: "state",
      width: 150,
      render: (text: string, record: any) => {
        if (text.toLocaleLowerCase() === "success") {
          return <Tag color="#87d068">{text}</Tag>;
        } else {
          return <Tag color="#f50">{text}</Tag>;
        }
      },
    },
    {
      title: "最近部署类型",
      dataIndex: "enviroment_type",
      key: "enviroment_type",
      width: 150,
    },
    {
      title: "最近部署环境名称",
      dataIndex: "enviroment_name",
      key: "enviroment_name",
      width: 150,
    },
    {
      title: "历史成功次数",
      key: "success_count",
      dataIndex: "success_count",
      width: 150,
      render: (text: string, record: any) => (
        <Flex>
          <CheckCircleFilled style={{ color: "green" }} />
          <span className="ml-1">
            {
              record.deploys.filter(
                (deploy: any) =>
                  (deploy.state as string).toLocaleLowerCase() === "success"
              ).length
            }
          </span>
        </Flex>
      ),
    },
    {
      title: "历史失败次数",
      key: "fail_count",
      dataIndex: "fail_count",
      width: 150,
      render: (text: string, record: any) => (
        <Flex>
          <CloseCircleFilled style={{ color: "red" }} />
          <span className="ml-1">
            {
              record.deploys.filter(
                (deploy: any) =>
                  (deploy.state as string).toLocaleLowerCase() !== "success"
              ).length
            }
          </span>
        </Flex>
      ),
    },
    {
      title: "操作",
      key: "action",
      width: 180,
      fixed: "right",
      render: (text: string, record: any) => (
        <Flex justify="space-around" align="center">
          <Button
            type="link"
            danger
            size="small"
            onClick={() => {
              Modal.error({
                title: "失败列表",
                closable: true,
                footer: null,
                width: "80%",
                okCancel: false,
                content: (
                  <Table
                    style={{ marginTop: "16px" }}
                    size={"small"}
                    pagination={{
                      showLessItems: true,
                    }}
                    bordered
                    scroll={{ x: 800 }}
                    dataSource={record.deploys.filter(
                      (deploy: any) => deploy.state.toLowerCase() !== "success"
                    )}
                    rowKey={(record) => record.id}
                    columns={columns}
                  />
                ),
              });
            }}
          >
            查看失败
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => {
              Modal.info({
                title: "部署历史",
                closable: true,
                footer: null,
                width: "80%",
                okCancel: false,
                content: (
                  <Table
                    style={{ marginTop: "16px" }}
                    size={"small"}
                    pagination={{
                      showLessItems: true,
                    }}
                    bordered
                    scroll={{ x: 800 }}
                    dataSource={record.deploys}
                    rowKey={(record) => record.id}
                    columns={columns}
                  />
                ),
              });
            }}
          >
            查看所有
          </Button>
        </Flex>
      ),
    },
  ];
}

export default useColumns;
