import { Tag, Button } from "antd";
import dayjs from "dayjs";
import React from "react";

function useEventColumns() {
  return [
    {
      title: "部署时间",
      dataIndex: "timestamp",
      key: "timestamp",
      render: (text: string, record: any) =>
        text ? dayjs(text).format("YYYY-MM-DD HH:mm:ss") : "N.A",
    },
    {
      title: "环境名称",
      dataIndex: "enviroment_name",
      key: "enviroment_name",
    },
    {
      title: "环境类型",
      dataIndex: "enviroment_type",
      key: "enviroment_type",
    },
    {
      title: "部署状态",
      dataIndex: "state",
      key: "state",
      render: (text: string, record: any) => {
        if (text.toLocaleLowerCase() === "success") {
          return <Tag color="#87d068">{text}</Tag>;
        } else {
          return <Tag color="#f50">{text}</Tag>;
        }
      },
    },
    {
      title: "Jenkins地址",
      dataIndex: "build_url",
      key: "build_url",
      render(text: string) {
        const parts = text.split("/");
        const jobNumber = parts[parts.length - 2];
        return (
          <Button type="link" href={text} target="_blank">
            #{jobNumber}
          </Button>
        );
      },
    },
  ];
}

export default useEventColumns;
