"use client";
import React, { useState } from "react";
import SearchArea from "./SearchArea";
import IssueTable from "./IssueTable";
import { Card, Segmented } from "antd";
import EventTable from "./EventTable";
import { useSearchParams } from "next/navigation";

function JenkinsPage() {
  const [dimension, setDimension] = useState<string>("事务维度");
  return (
    <>
      <SearchArea />
      <Card
        style={{
          marginLeft: "8px",
          marginRight: "8px",
          height: "calc(100% - 220px)",
          overflowY: "auto",
          maxWidth: "calc(100vw - 218px)",
        }}
      >
        <Segmented
          options={["事务维度", "事件维度"]}
          style={{ marginBottom: "8px" }}
          onChange={(value) => setDimension(value)}
          value={dimension}
        />
        {dimension === "事务维度" && <IssueTable />}
        {dimension === "事件维度" && <EventTable />}
      </Card>
    </>
  );
}

export default JenkinsPage;
