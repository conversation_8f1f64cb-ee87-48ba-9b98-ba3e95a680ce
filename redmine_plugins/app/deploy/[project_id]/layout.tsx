import React, { PropsWithChildren, Suspense } from "react";
import { getProjectInfo } from "./api/redmine";
import { Result } from "antd";
import BriefInfo from "./components/BriefInfo";

type Props = PropsWithChildren<{ params: any }>;

async function layout({ children, params }: Props) {
  const project_id = +params.project_id;
  const result = await getProjectInfo(project_id);
  if (result.error) {
    return (
      <Result
        status="500"
        title="500"
        subTitle={JSON.stringify(result.error)}
      />
    );
  } else {
    return (
      <Suspense>
        <BriefInfo project={result.project} />
        {children}
      </Suspense>
    );
  }
}

export default layout;
