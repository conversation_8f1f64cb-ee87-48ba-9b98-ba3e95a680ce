import React, { Suspense } from "react";
import { Result } from "antd";
import JenkinsPage from "./components/jenkins";
import GerritPage from "./components/gerrit";
import GanttPage from "./components/gantt/index.bak";
import VisTimeline from "./components/gantt/index";

async function Page({
  searchParams,
}: {
  params: { project_id: string };
  searchParams: { [key: string]: string | undefined };
}) {
  const component = searchParams.component;

  if (component === "jenkins") {
    return <JenkinsPage />;
  } else if (component === "gerrit") {
    return <GerritPage />;
  } else if (component === "dashboard") {
  } else if (component === "gantt") {
    return (
      <Suspense>
        {/* <GanttPage /> */}
        <VisTimeline />
      </Suspense>
    );
  } else {
    return (
      <Result
        status="404"
        title="404"
        subTitle={"不存在此组件: " + component}
      />
    );
  }
}

export default Page;
