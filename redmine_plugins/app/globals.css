@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

.ant-form-item {
  margin-bottom: 0px !important;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

* {
  scrollbar-width: thin !important;
}

.vis-delete {
  display: none;
}

.vis-drag-left {
  background-color: #1890ff;
  border: 1px solid #1890ff;
  color: #fff;
  opacity: 0;
  width: 8px !important;
  border-radius: 3px;
  transition: opacity 0.3s ease;
  cursor: default !important;
}

.vis-drag-right {
  background-color: #1890ff;
  border: 1px solid #1890ff;
  color: #fff;
  opacity: 0;
  width: 8px !important;
  border-radius: 3px;
  transition: opacity 0.3s ease;
  cursor: default !important;
}

.vis-drag-left:hover {
  background-color: #1890ff;
  border: 1px solid #1890ff;
  color: #fff;
  opacity: 1;
  visibility: visible;
  width: 8px !important;
  border-radius: 3px;
  transition: opacity 0.3s ease;
  cursor: default !important;
}

.vis-drag-left:active {
  background-color: #1890ff;
  border: 1px solid #1890ff;
  color: #fff;
  opacity: 1;
  visibility: visible;
  width: 8px !important;
  border-radius: 3px;
  transition: opacity 0.3s ease;
  cursor: default !important;
}

.vis-drag-right:hover {
  background-color: #1890ff;
  border: 1px solid #1890ff;
  color: #fff;
  opacity: 1;
  visibility: visible;
  width: 8px !important;
  border-radius: 3px;
  transition: opacity 0.3s ease;
  cursor: default !important;
}

.vis-drag-right:active {
  background-color: #1890ff;
  border: 1px solid #1890ff;
  color: #fff;
  opacity: 1;
  visibility: visible;
  width: 8px !important;
  border-radius: 3px;
  transition: opacity 0.3s ease;
  cursor: default !important;
}

.vis-drag-left:hover .vis-delete {
  display: block;
}

.vis-drag-right:hover .vis-delete {
  display: block;
  opacity: 1;
  visibility: visible;
  transition: opacity 0.3s ease;
}

.vis-editable.vis-selected {
  cursor: default !important;
}

.vis-item .vis-drag-center {
  cursor: default !important;
}

.vis-item.vis-selected .vis-drag-center {
  cursor: default !important;
}

.vis-item.vis-selected .vis-drag-left {
  cursor: default !important;
}

.vis-item.vis-selected .vis-drag-right {
  cursor: default !important;
}
