import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import "@bitnoi.se/react-scheduler/dist/style.css";
import "vis-timeline/styles/vis-timeline-graph2d.min.css";
import { Flex, Layout } from "antd";
import { AntdRegistry } from "@ant-design/nextjs-registry";
import SiderMenu from "./components/SiderMenu";
import { ItemType, MenuItemType } from "antd/es/menu/interface";
import {
  CiCircleOutlined,
  DashboardOutlined,
  GithubOutlined,
  TeamOutlined
} from "@ant-design/icons";
import { Suspense } from "react";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

const menuItems: ItemType<MenuItemType>[] = [
  {
    key: "dashboard",
    label: "项目看板",
    icon: <DashboardOutlined />,
  },
  {
    key: "jenkins",
    label: "Jenkins部署信息",
    icon: <CiCircleOutlined />,
  },
  {
    key: "gerrit",
    label: "Gerrit部署信息",
    icon: <GithubOutlined />,
  },
  {
    key: "gantt",
    label: "事务甘特图",
    icon: <TeamOutlined />,
  },
];

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AntdRegistry>
          <Layout className="w-screen">
            <Flex className="w-screen h-screen">
              <Suspense>
                <SiderMenu items={menuItems} />
              </Suspense>
              <div style={{ flex: 1 }}>{children}</div>
            </Flex>
          </Layout>
        </AntdRegistry>
      </body>
    </html>
  );
}
