generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model Project_quality_audit {
  PUID_VALUE                                          String   @db.VarChar(100)
  SWPM_Value                                          String   @db.VarChar(100)
  QE_Value                                            String?  @db.VarChar(100)
  Date_Value                                          DateTime @db.Date
  QG_Value                                            String   @db.VarChar(100)
  SOP_Date_Value                                      String?  @db.VarChar(100)
  Redmine_Link_Value                                  String?  @db.VarChar(100)
  Feature_Configured_Value                            Boolean
  Feature_Basic_Prop_Configured_Value                 String?  @db.VarChar(100)
  Feature_Recc_Prop_Configured_Value                  String?  @db.VarChar(100)
  Task_Configured_Value                               Boolean?
  Task_Basic_Prop_Configured_Value                    String?  @db.VarChar(100)
  Task_Recc_Prop_Configured_Value                     String?  @db.VarChar(100)
  Bug_Configured_Value                                Boolean?
  Bug_Basic_Prop_Configured_Value                     String?  @db.VarChar(100)
  Bug_Recc_Prop_Configured_Value                      String?  @db.VarChar(100)
  OPL_Configured_Value                                Boolean?
  OPL_Basic_Prop_Configured_Value                     String?  @db.VarChar(100)
  OPL_Recc_Prop_Configured_Value                      String?  @db.VarChar(100)
  Risk_Configured_Value                               Boolean?
  Risk_Basic_Prop_Configured_Value                    String?  @db.VarChar(100)
  Risk_Recc_Prop_Configured_Value                     String?  @db.VarChar(100)
  CR_Configured_Value                                 Boolean?
  CR_Basic_Prop_Configured_Value                      String?  @db.VarChar(100)
  CR_Recc_Prop_Configured_Value                       String?  @db.VarChar(100)
  Integration_Configured_Value                        Boolean?
  Integration_Basic_Prop_Configured_Value             String?  @db.VarChar(100)
  Integration_Recc_Prop_Configured_Value              String?  @db.VarChar(100)
  OTD_Value_this_month                                String?  @db.VarChar(100)
  OTD_Value_Overall                                   String?  @db.VarChar(100)
  OTD_Result                                          String?  @db.VarChar(100)
  Commit_Msg_Pass_Rate_Value_this_month               String?  @db.VarChar(100)
  Commit_Msg_Pass_Rate_Value_Overall                  String?  @db.VarChar(100)
  Commit_Msg_Pass_Rate_Result                         String?  @db.VarChar(100)
  Review_Pass_Rate_Value_this_month                   String?  @db.VarChar(100)
  Review_Pass_Rate_Value_Overall                      String?  @db.VarChar(100)
  Review_Pass_Rate_Result                             String?  @db.VarChar(100)
  Integration_Test_Pass_Rate_Value_this_month         String?  @db.VarChar(100)
  Integration_Test_Pass_Rate_Value_Overall            String?  @db.VarChar(100)
  Integration_Test_Pass_Rate_Result                   String?  @db.VarChar(100)
  QAC_Warning_Delta_Value_this_month                  String?  @db.VarChar(100)
  QAC_Warning_Delta_Value_Overall                     String?  @db.VarChar(100)
  QAC_Warning_Delta_Result                            String?  @db.VarChar(100)
  Redmine_Unplanned_Issue_Count_Value_this_month      String?  @db.VarChar(100)
  Redmine_Unplanned_Issue_Count_Value_Overall         String?  @db.VarChar(100)
  Redmine_Unplanned_Issue_Count_Result                String?  @db.VarChar(100)
  Redmine_Overdue_Issue_Count_Value_this_month        String?  @db.VarChar(100)
  Redmine_Overdue_Issue_Count_Value_Overall           String?  @db.VarChar(100)
  Redmine_Overdue_Issue_Count_Result                  String?  @db.VarChar(100)
  Redmine_Over20Days_Count_Value_this_month           String?  @db.VarChar(100)
  Redmine_Over20Days_Count_Value_Overall              String?  @db.VarChar(100)
  Redmine_Over20Days_Count_Result                     String?  @db.VarChar(100)
  Redmine_Resolve_Avg_Duration_Count_Value_this_month String?  @db.VarChar(100)
  Redmine_Resolve_Avg_Duration_Count_Value_Overall    String?  @db.VarChar(100)
  Redmine_Resolve_Avg_Duration_Count_Result           String?  @db.VarChar(100)
  Redmine_Reopen_Issue_Count_Value_this_month         String?  @db.VarChar(100)
  Redmine_Reopen_Issue_Count_Value_Overall            String?  @db.VarChar(100)
  Redmine_Reopen_Issue_Count_Result                   String?  @db.VarChar(100)
  Redmine_Resolve_Rate_Value_this_month               String?  @db.VarChar(100)
  Redmine_Resolve_Rate_Value_Overall                  String?  @db.VarChar(100)
  Redmine_Resolve_Rate_Result                         String?  @db.VarChar(100)

  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model QG_data {
  project_name String? @db.VarChar(100)
  QG_stage     String? @db.VarChar(100)
  SOP_time     String? @db.VarChar(100)
  cost_center  String? @db.VarChar(100)
  complete     String? @db.VarChar(100)
  perfect      String? @db.VarChar(100)

  @@ignore
}

model Web_userinfo {
  id           BigInt @id @default(autoincrement())
  username     String @db.VarChar(32)
  email        String @db.VarChar(32)
  mobile_phone String @db.VarChar(32)
  password     String @db.VarChar(32)
}

model ab_permission {
  id                 Int                  @id @default(autoincrement())
  name               String               @unique(map: "name") @db.VarChar(100)
  ab_permission_view ab_permission_view[]
}

model ab_permission_view {
  id                      Int                       @id @default(autoincrement())
  permission_id           Int?
  view_menu_id            Int?
  ab_permission           ab_permission?            @relation(fields: [permission_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "ab_permission_view_ibfk_1")
  ab_view_menu            ab_view_menu?             @relation(fields: [view_menu_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "ab_permission_view_ibfk_2")
  ab_permission_view_role ab_permission_view_role[]

  @@unique([permission_id, view_menu_id], map: "permission_id")
  @@index([view_menu_id], map: "view_menu_id")
}

model ab_permission_view_role {
  id                 Int                 @id @default(autoincrement())
  permission_view_id Int?
  role_id            Int?
  ab_permission_view ab_permission_view? @relation(fields: [permission_view_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "ab_permission_view_role_ibfk_1")
  ab_role            ab_role?            @relation(fields: [role_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "ab_permission_view_role_ibfk_2")

  @@unique([permission_view_id, role_id], map: "permission_view_id")
  @@index([role_id], map: "role_id")
}

model ab_register_user {
  id                Int       @id @default(autoincrement())
  first_name        String    @db.VarChar(64)
  last_name         String    @db.VarChar(64)
  username          String    @unique(map: "username") @db.VarChar(64)
  password          String?   @db.VarChar(256)
  email             String    @db.VarChar(64)
  registration_date DateTime? @db.DateTime(0)
  registration_hash String?   @db.VarChar(256)
}

model ab_role {
  id                      Int                       @id @default(autoincrement())
  name                    String                    @unique(map: "name") @db.VarChar(64)
  ab_permission_view_role ab_permission_view_role[]
  ab_user_role            ab_user_role[]
}

model ab_user {
  id                                           Int            @id @default(autoincrement())
  first_name                                   String         @db.VarChar(64)
  last_name                                    String         @db.VarChar(64)
  username                                     String         @unique(map: "username") @db.VarChar(64)
  password                                     String?        @db.VarChar(256)
  active                                       Boolean?
  email                                        String         @unique(map: "email") @db.VarChar(64)
  last_login                                   DateTime?      @db.DateTime(0)
  login_count                                  Int?
  fail_login_count                             Int?
  created_on                                   DateTime?      @db.DateTime(0)
  changed_on                                   DateTime?      @db.DateTime(0)
  created_by_fk                                Int?
  changed_by_fk                                Int?
  ab_user_ab_user_created_by_fkToab_user       ab_user?       @relation("ab_user_created_by_fkToab_user", fields: [created_by_fk], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "ab_user_ibfk_1")
  other_ab_user_ab_user_created_by_fkToab_user ab_user[]      @relation("ab_user_created_by_fkToab_user")
  ab_user_ab_user_changed_by_fkToab_user       ab_user?       @relation("ab_user_changed_by_fkToab_user", fields: [changed_by_fk], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "ab_user_ibfk_2")
  other_ab_user_ab_user_changed_by_fkToab_user ab_user[]      @relation("ab_user_changed_by_fkToab_user")
  ab_user_role                                 ab_user_role[]

  @@index([changed_by_fk], map: "changed_by_fk")
  @@index([created_by_fk], map: "created_by_fk")
}

model ab_user_role {
  id      Int      @id @default(autoincrement())
  user_id Int?
  role_id Int?
  ab_user ab_user? @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "ab_user_role_ibfk_1")
  ab_role ab_role? @relation(fields: [role_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "ab_user_role_ibfk_2")

  @@unique([user_id, role_id], map: "user_id")
  @@index([role_id], map: "role_id")
}

model ab_view_menu {
  id                 Int                  @id @default(autoincrement())
  name               String               @unique(map: "name") @db.VarChar(250)
  ab_permission_view ab_permission_view[]
}

model agile_colors {
  id             Int     @id @default(autoincrement())
  container_id   Int?
  container_type String? @db.VarChar(255)
  color          String? @db.VarChar(255)

  @@index([container_id], map: "index_agile_colors_on_container_id")
  @@index([container_type], map: "index_agile_colors_on_container_type")
}

model agile_data {
  id              Int  @id @default(autoincrement())
  issue_id        Int?
  position        Int?
  story_points    Int?
  agile_sprint_id Int?

  @@index([issue_id], map: "index_agile_data_on_issue_id")
  @@index([position], map: "index_agile_data_on_position")
}

model agile_sprints {
  id          Int      @id @default(autoincrement())
  project_id  Int?
  name        String   @db.VarChar(255)
  description String?  @db.Text
  status      Int      @default(0)
  start_date  DateTime @db.Date
  end_date    DateTime @db.Date
  created_at  DateTime @db.DateTime(0)
  updated_at  DateTime @db.DateTime(0)
  sharing     Int      @default(0)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model attachments {
  id             Int       @id @default(autoincrement())
  container_id   Int?
  container_type String?   @db.VarChar(30)
  filename       String?   @db.VarChar(255)
  disk_filename  String?   @db.VarChar(255)
  filesize       BigInt    @default(0)
  content_type   String?   @db.VarChar(255)
  digest         String?   @db.VarChar(64)
  downloads      Int       @default(0)
  author_id      Int       @default(0)
  created_on     DateTime? @db.DateTime(0)
  description    String?   @db.VarChar(255)
  disk_directory String?   @db.VarChar(255)

  @@index([author_id], map: "index_attachments_on_author_id")
  @@index([container_id, container_type], map: "index_attachments_on_container_id_and_container_type")
  @@index([created_on], map: "index_attachments_on_created_on")
  @@index([disk_filename(length: 191)], map: "index_attachments_on_disk_filename")
}

model auth_group {
  id                     Int                      @id @default(autoincrement())
  name                   String                   @unique(map: "name") @db.VarChar(150)
  auth_group_permissions auth_group_permissions[]
  auth_user_groups       auth_user_groups[]
}

model auth_group_permissions {
  id              BigInt          @id @default(autoincrement())
  group_id        Int
  permission_id   Int
  auth_permission auth_permission @relation(fields: [permission_id], references: [id], onUpdate: Restrict, map: "auth_group_permissio_permission_id_84c5c92e_fk_auth_perm")
  auth_group      auth_group      @relation(fields: [group_id], references: [id], onUpdate: Restrict, map: "auth_group_permissions_group_id_b120cbf9_fk_auth_group_id")

  @@unique([group_id, permission_id], map: "auth_group_permissions_group_id_permission_id_0cd325b0_uniq")
  @@index([permission_id], map: "auth_group_permissio_permission_id_84c5c92e_fk_auth_perm")
}

model auth_permission {
  id                         Int                          @id @default(autoincrement())
  name                       String                       @db.VarChar(255)
  content_type_id            Int
  codename                   String                       @db.VarChar(100)
  auth_group_permissions     auth_group_permissions[]
  django_content_type        django_content_type          @relation(fields: [content_type_id], references: [id], onUpdate: Restrict, map: "auth_permission_content_type_id_2f476e4b_fk_django_co")
  auth_user_user_permissions auth_user_user_permissions[]

  @@unique([content_type_id, codename], map: "auth_permission_content_type_id_codename_01ab375a_uniq")
}

model auth_sources {
  id                Int     @id @default(autoincrement())
  type              String? @db.VarChar(30)
  name              String? @db.VarChar(60)
  host              String? @db.VarChar(60)
  port              Int?
  account           String? @db.VarChar(255)
  account_password  String? @db.VarChar(255)
  base_dn           String? @db.VarChar(255)
  attr_login        String? @db.VarChar(30)
  attr_firstname    String? @db.VarChar(30)
  attr_lastname     String? @db.VarChar(30)
  attr_mail         String? @db.VarChar(30)
  onthefly_register Boolean @default(false)
  tls               Boolean @default(false)
  filter            String? @db.Text
  timeout           Int?

  @@index([id, type], map: "index_auth_sources_on_id_and_type")
}

model auth_user {
  id                         Int                          @id @default(autoincrement())
  password                   String                       @db.VarChar(128)
  last_login                 DateTime?                    @db.DateTime(6)
  is_superuser               Boolean
  username                   String                       @unique(map: "username") @db.VarChar(150)
  first_name                 String                       @db.VarChar(150)
  last_name                  String                       @db.VarChar(150)
  email                      String                       @db.VarChar(254)
  is_staff                   Boolean
  is_active                  Boolean
  date_joined                DateTime                     @db.DateTime(6)
  auth_user_groups           auth_user_groups[]
  auth_user_user_permissions auth_user_user_permissions[]
  django_admin_log           django_admin_log[]
}

model auth_user_groups {
  id         BigInt     @id @default(autoincrement())
  user_id    Int
  group_id   Int
  auth_group auth_group @relation(fields: [group_id], references: [id], onUpdate: Restrict, map: "auth_user_groups_group_id_97559544_fk_auth_group_id")
  auth_user  auth_user  @relation(fields: [user_id], references: [id], onUpdate: Restrict, map: "auth_user_groups_user_id_6a12ed8b_fk_auth_user_id")

  @@unique([user_id, group_id], map: "auth_user_groups_user_id_group_id_94350c0c_uniq")
  @@index([group_id], map: "auth_user_groups_group_id_97559544_fk_auth_group_id")
}

model auth_user_user_permissions {
  id              BigInt          @id @default(autoincrement())
  user_id         Int
  permission_id   Int
  auth_permission auth_permission @relation(fields: [permission_id], references: [id], onUpdate: Restrict, map: "auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm")
  auth_user       auth_user       @relation(fields: [user_id], references: [id], onUpdate: Restrict, map: "auth_user_user_permissions_user_id_a95ead1b_fk_auth_user_id")

  @@unique([user_id, permission_id], map: "auth_user_user_permissions_user_id_permission_id_14a6b632_uniq")
  @@index([permission_id], map: "auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model boards {
  id              Int     @id @default(autoincrement())
  project_id      Int
  name            String? @db.VarChar(255)
  description     String? @db.VarChar(255)
  position        Int?
  topics_count    Int     @default(0)
  messages_count  Int     @default(0)
  last_message_id Int?
  parent_id       Int?

  @@index([project_id], map: "boards_project_id")
  @@index([last_message_id], map: "index_boards_on_last_message_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model changes {
  id            Int     @id @default(autoincrement())
  changeset_id  Int
  action        String? @db.VarChar(1)
  path          String? @db.Text
  from_path     String? @db.Text
  from_revision String? @db.VarChar(255)
  revision      String? @db.VarChar(255)
  branch        String? @db.VarChar(255)

  @@index([changeset_id], map: "changesets_changeset_id")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model changeset_parents {
  changeset_id Int
  parent_id    Int

  @@index([changeset_id], map: "changeset_parents_changeset_ids")
  @@index([parent_id], map: "changeset_parents_parent_ids")
  @@ignore
}

model changesets {
  id            Int       @id @default(autoincrement())
  repository_id Int
  revision      String    @db.VarChar(255)
  committer     String?   @db.VarChar(255)
  committed_on  DateTime  @db.DateTime(0)
  comments      String?   @db.LongText
  commit_date   DateTime? @db.Date
  scmid         String?   @db.VarChar(255)
  user_id       Int?

  @@unique([repository_id, revision], map: "changesets_repos_rev")
  @@index([repository_id, scmid(length: 191)], map: "changesets_repos_scmid")
  @@index([committed_on], map: "index_changesets_on_committed_on")
  @@index([repository_id], map: "index_changesets_on_repository_id")
  @@index([user_id], map: "index_changesets_on_user_id")
}

model changesets_issues {
  changeset_id Int
  issue_id     Int

  @@unique([changeset_id, issue_id], map: "changesets_issues_ids")
  @@index([issue_id], map: "index_changesets_issues_on_issue_id")
}

model comments {
  id             Int      @id @default(autoincrement())
  commented_type String?  @db.VarChar(30)
  commented_id   Int      @default(0)
  author_id      Int      @default(0)
  comments       String?  @db.Text
  created_on     DateTime @db.DateTime(0)
  updated_on     DateTime @db.DateTime(0)

  @@index([author_id], map: "index_comments_on_author_id")
  @@index([commented_id, commented_type], map: "index_comments_on_commented_id_and_commented_type")
}

model custom_field_enumerations {
  id              Int     @id @default(autoincrement())
  custom_field_id Int
  name            String? @db.VarChar(255)
  active          Boolean @default(true)
  position        Int     @default(1)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model custom_fields {
  id              Int      @id @default(autoincrement())
  type            String   @default("") @db.VarChar(30)
  name            String   @default("") @db.VarChar(30)
  field_format    String   @default("") @db.VarChar(30)
  possible_values String?  @db.MediumText
  regexp          String?  @default("") @db.VarChar(255)
  min_length      Int?
  max_length      Int?
  is_required     Boolean  @default(false)
  is_for_all      Boolean  @default(false)
  is_filter       Boolean  @default(false)
  position        Int?
  searchable      Boolean? @default(false)
  default_value   String?  @db.MediumText
  editable        Boolean? @default(true)
  visible         Boolean  @default(true)
  multiple        Boolean? @default(false)
  format_store    String?  @db.MediumText
  description     String?  @db.MediumText

  @@index([id, type], map: "index_custom_fields_on_id_and_type")
}

model custom_fields_projects {
  custom_field_id Int @default(0)
  project_id      Int @default(0)

  @@unique([custom_field_id, project_id], map: "index_custom_fields_projects_on_custom_field_id_and_project_id")
}

model custom_fields_roles {
  custom_field_id Int
  role_id         Int

  @@unique([custom_field_id, role_id], map: "custom_fields_roles_ids")
}

model custom_fields_trackers {
  custom_field_id Int @default(0)
  tracker_id      Int @default(0)

  @@unique([custom_field_id, tracker_id], map: "index_custom_fields_trackers_on_custom_field_id_and_tracker_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model custom_values {
  id              Int     @id @default(autoincrement())
  customized_type String? @db.VarChar(30)
  customized_id   Int     @default(0)
  custom_field_id Int     @default(0)
  value           String? @db.Text

  @@index([customized_type, customized_id], map: "custom_values_customized")
  @@index([custom_field_id], map: "index_custom_values_on_custom_field_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model daliy_CR_data {
  id           Int       @id @default(autoincrement())
  date_time    DateTime? @db.Date
  project_name String    @default("") @db.Char(100)
  sum_num      Int?
  kind         Int
}

model daliy_EST_sum {
  id              Int       @id @default(autoincrement())
  date_time       DateTime? @db.Date
  project_name    String    @default("") @db.Char(200)
  sum_num         Int?
  DICount         Float?
  OpenBugCount    Int?
  S_Count         Int?
  A_Count         Int?
  B_Count         Int?
  C_Count         Int?
  Version_Count   Int?
  OPL_Count       Int?
  CR_Count        Int?
  CR_Open_Count   Int?
  OPL_Open_Count  Int?
  Version_duetime DateTime? @db.Date
}

model daliy_issues_sum {
  id           Int       @id @default(autoincrement())
  date_time    DateTime? @db.Date
  project_name String    @default("") @db.Char(100)
  sum_num      Int?
}

model daliy_open_issues_count {
  id           Int       @id @default(autoincrement())
  date_time    DateTime? @db.Date
  project_name String    @default("") @db.Char(100)
  sum_num      Int?
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model data_schedule_release {
  project String? @db.VarChar(100)
  time    String? @db.VarChar(100)
  value   String? @db.VarChar(100)

  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model data_schedule_release_new {
  project_name String? @db.VarChar(100)
  create_time  String? @db.VarChar(100)
  version      String? @db.VarChar(100)
  QGC0         String? @db.VarChar(100)
  A_Sample     String? @db.VarChar(100)
  B_Sample     String? @db.VarChar(100)
  QGC1         String? @db.VarChar(100)
  C_Sample     String? @db.VarChar(100)
  QGC2         String? @db.VarChar(100)
  D_Sample     String? @db.VarChar(100)
  QGC3         String? @db.VarChar(100)
  QGC4         String? @db.VarChar(100)
  SOP_BiTECH   String? @db.VarChar(100)
  SOP_OEM      String? @db.VarChar(100)
  QGC5         String? @db.VarChar(100)

  @@ignore
}

model django_admin_log {
  id                  Int                  @id @default(autoincrement())
  action_time         DateTime             @db.DateTime(6)
  object_id           String?              @db.LongText
  object_repr         String               @db.VarChar(200)
  action_flag         Int                  @db.UnsignedSmallInt
  change_message      String               @db.LongText
  content_type_id     Int?
  user_id             Int
  django_content_type django_content_type? @relation(fields: [content_type_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "django_admin_log_content_type_id_c4bce8eb_fk_django_co")
  auth_user           auth_user            @relation(fields: [user_id], references: [id], onUpdate: Restrict, map: "django_admin_log_user_id_c564eba6_fk_auth_user_id")

  @@index([content_type_id], map: "django_admin_log_content_type_id_c4bce8eb_fk_django_co")
  @@index([user_id], map: "django_admin_log_user_id_c564eba6_fk_auth_user_id")
}

model django_content_type {
  id               Int                @id @default(autoincrement())
  app_label        String             @db.VarChar(100)
  model            String             @db.VarChar(100)
  auth_permission  auth_permission[]
  django_admin_log django_admin_log[]

  @@unique([app_label, model], map: "django_content_type_app_label_model_76bd3d3b_uniq")
}

model django_migrations {
  id      BigInt   @id @default(autoincrement())
  app     String   @db.VarChar(255)
  name    String   @db.VarChar(255)
  applied DateTime @db.DateTime(6)
}

model django_session {
  session_key  String   @id @db.VarChar(40)
  session_data String   @db.LongText
  expire_date  DateTime @db.DateTime(6)

  @@index([expire_date], map: "django_session_expire_date_a5c62663")
}

model documents {
  id          Int       @id @default(autoincrement())
  project_id  Int       @default(0)
  category_id Int       @default(0)
  title       String?   @db.VarChar(255)
  description String?   @db.Text
  created_on  DateTime? @db.DateTime(0)

  @@index([project_id], map: "documents_project_id")
  @@index([category_id], map: "index_documents_on_category_id")
  @@index([created_on], map: "index_documents_on_created_on")
}

model email_addresses {
  id         Int      @id @default(autoincrement())
  user_id    Int
  address    String?  @db.VarChar(255)
  is_default Boolean  @default(false)
  notify     Boolean  @default(true)
  created_on DateTime @db.DateTime(0)
  updated_on DateTime @db.DateTime(0)

  @@index([user_id], map: "index_email_addresses_on_user_id")
}

model employee_admin {
  id             BigInt           @id @default(autoincrement())
  username       String           @db.VarChar(32)
  password       String           @db.VarChar(32)
  employee_order employee_order[]
  employee_task  employee_task[]
}

model employee_boss {
  id   BigInt @id @default(autoincrement())
  name String @db.VarChar(32)
  age  Int
  img  String @db.VarChar(128)
}

model employee_city {
  id    BigInt @id @default(autoincrement())
  name  String @db.VarChar(32)
  count Int
  img   String @db.VarChar(128)
}

model employee_department {
  id                BigInt              @id @default(autoincrement())
  title             String              @db.VarChar(32)
  employee_userinfo employee_userinfo[]
}

model employee_order {
  id             BigInt         @id @default(autoincrement())
  oid            String         @db.VarChar(64)
  title          String         @db.VarChar(32)
  price          Int
  status         Int            @db.SmallInt
  admin_id       BigInt
  employee_admin employee_admin @relation(fields: [admin_id], references: [id], onUpdate: Restrict, map: "employee_order_admin_id_37a370d3_fk_employee_admin_id")

  @@index([admin_id], map: "employee_order_admin_id_37a370d3_fk_employee_admin_id")
}

model employee_prettynum {
  id     BigInt @id @default(autoincrement())
  mobile String @db.VarChar(11)
  price  Int
  level  Int    @db.SmallInt
  status Int    @db.SmallInt
}

model employee_task {
  id             BigInt         @id @default(autoincrement())
  level          Int            @db.SmallInt
  title          String         @db.VarChar(64)
  detail         String         @db.LongText
  user_id        BigInt
  employee_admin employee_admin @relation(fields: [user_id], references: [id], onUpdate: Restrict, map: "employee_task_user_id_5b26fb48_fk_employee_admin_id")

  @@index([user_id], map: "employee_task_user_id_5b26fb48_fk_employee_admin_id")
}

model employee_userinfo {
  id                  BigInt               @id @default(autoincrement())
  name                String               @db.VarChar(16)
  password            String               @db.VarChar(64)
  age                 Int
  account             Decimal              @db.Decimal(10, 2)
  create_time         DateTime             @db.Date
  gender              Int                  @db.SmallInt
  depart_id           BigInt?
  employee_department employee_department? @relation(fields: [depart_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "employee_userinfo_depart_id_26f7e7cd_fk_employee_department_id")

  @@index([depart_id], map: "employee_userinfo_depart_id_26f7e7cd_fk_employee_department_id")
}

model enabled_modules {
  id         Int     @id @default(autoincrement())
  project_id Int?
  name       String? @db.VarChar(255)

  @@index([project_id], map: "enabled_modules_project_id")
}

model enumerations {
  id            Int     @id @default(autoincrement())
  name          String? @db.VarChar(30)
  position      Int?
  is_default    Boolean @default(false)
  type          String? @db.VarChar(255)
  active        Boolean @default(true)
  project_id    Int?
  parent_id     Int?
  position_name String? @db.VarChar(30)

  @@index([id, type(length: 191)], map: "index_enumerations_on_id_and_type")
  @@index([project_id], map: "index_enumerations_on_project_id")
}

model favorite_projects {
  id         Int  @id @default(autoincrement())
  project_id Int?
  user_id    Int?

  @@index([project_id, user_id], map: "index_favorite_projects_on_project_id_and_user_id")
}

model groups_users {
  group_id Int
  user_id  Int

  @@unique([group_id, user_id], map: "groups_users_ids")
}

model import_in_progresses {
  id         Int       @id @default(autoincrement())
  user_id    Int
  quote_char String?   @db.VarChar(8)
  col_sep    String?   @db.VarChar(8)
  encoding   String?   @db.VarChar(64)
  created    DateTime? @db.DateTime(0)
  csv_data   Bytes?    @db.MediumBlob
}

model import_items {
  id        Int     @id @default(autoincrement())
  import_id Int
  position  Int
  obj_id    Int?
  message   String? @db.Text
}

model imports {
  id          Int      @id @default(autoincrement())
  type        String?  @db.VarChar(255)
  user_id     Int
  filename    String?  @db.VarChar(255)
  settings    String?  @db.Text
  total_items Int?
  finished    Boolean  @default(false)
  created_at  DateTime @db.DateTime(0)
  updated_at  DateTime @db.DateTime(0)
}

model issue_categories {
  id             Int     @id @default(autoincrement())
  project_id     Int     @default(0)
  name           String? @db.VarChar(60)
  assigned_to_id Int?

  @@index([assigned_to_id], map: "index_issue_categories_on_assigned_to_id")
  @@index([project_id], map: "issue_categories_project_id")
}

model issue_relations {
  id            Int     @id @default(autoincrement())
  issue_from_id Int
  issue_to_id   Int
  relation_type String? @db.VarChar(255)
  delay         Int?

  @@unique([issue_from_id, issue_to_id], map: "index_issue_relations_on_issue_from_id_and_issue_to_id")
  @@index([issue_from_id], map: "index_issue_relations_on_issue_from_id")
  @@index([issue_to_id], map: "index_issue_relations_on_issue_to_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model issue_statuses {
  id                 Int     @id @default(autoincrement())
  name               String? @db.VarChar(30)
  is_closed          Boolean @default(false)
  position           Int?
  default_done_ratio Int?

  @@index([is_closed], map: "index_issue_statuses_on_is_closed")
  @@index([position], map: "index_issue_statuses_on_position")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model issues {
  id               Int       @id @default(autoincrement())
  tracker_id       Int
  project_id       Int
  subject          String?   @db.VarChar(255)
  description      String?   @db.LongText
  due_date         DateTime? @db.Date
  category_id      Int?
  status_id        Int
  assigned_to_id   Int?
  priority_id      Int
  fixed_version_id Int?
  author_id        Int
  lock_version     Int       @default(0)
  created_on       DateTime? @db.DateTime(0)
  updated_on       DateTime? @db.DateTime(0)
  start_date       DateTime? @db.Date
  done_ratio       Int       @default(0)
  estimated_hours  Float?    @db.Float
  parent_id        Int?
  root_id          Int?
  lft              Int?
  rgt              Int?
  is_private       Boolean   @default(false)
  closed_on        DateTime? @db.DateTime(0)

  @@index([assigned_to_id], map: "index_issues_on_assigned_to_id")
  @@index([author_id], map: "index_issues_on_author_id")
  @@index([category_id], map: "index_issues_on_category_id")
  @@index([created_on], map: "index_issues_on_created_on")
  @@index([fixed_version_id], map: "index_issues_on_fixed_version_id")
  @@index([parent_id], map: "index_issues_on_parent_id")
  @@index([priority_id], map: "index_issues_on_priority_id")
  @@index([root_id, lft, rgt], map: "index_issues_on_root_id_and_lft_and_rgt")
  @@index([status_id], map: "index_issues_on_status_id")
  @@index([tracker_id], map: "index_issues_on_tracker_id")
  @@index([project_id], map: "issues_project_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model journal_details {
  id         Int     @id @default(autoincrement())
  journal_id Int     @default(0)
  property   String? @db.VarChar(30)
  prop_key   String? @db.VarChar(30)
  old_value  String? @db.LongText
  value      String? @db.LongText

  @@index([journal_id], map: "journal_details_journal_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model journals {
  id               Int      @id @default(autoincrement())
  journalized_id   Int      @default(0)
  journalized_type String?  @db.VarChar(30)
  user_id          Int      @default(0)
  notes            String?  @db.LongText
  created_on       DateTime @db.DateTime(0)
  private_notes    Boolean  @default(false)

  @@index([created_on], map: "index_journals_on_created_on")
  @@index([journalized_id], map: "index_journals_on_journalized_id")
  @@index([user_id], map: "index_journals_on_user_id")
  @@index([journalized_id, journalized_type], map: "journals_journalized_id")
}

model member_roles {
  id             Int  @id @default(autoincrement())
  member_id      Int
  role_id        Int
  inherited_from Int?

  @@index([inherited_from], map: "index_member_roles_on_inherited_from")
  @@index([member_id], map: "index_member_roles_on_member_id")
  @@index([role_id], map: "index_member_roles_on_role_id")
}

model members {
  id                Int       @id @default(autoincrement())
  user_id           Int       @default(0)
  project_id        Int       @default(0)
  created_on        DateTime? @db.DateTime(0)
  mail_notification Boolean   @default(false)

  @@unique([user_id, project_id], map: "index_members_on_user_id_and_project_id")
  @@index([project_id], map: "index_members_on_project_id")
  @@index([user_id], map: "index_members_on_user_id")
}

model messages {
  id            Int      @id @default(autoincrement())
  board_id      Int
  parent_id     Int?
  subject       String?  @db.VarChar(255)
  content       String?  @db.Text
  author_id     Int?
  replies_count Int      @default(0)
  last_reply_id Int?
  created_on    DateTime @db.DateTime(0)
  updated_on    DateTime @db.DateTime(0)
  locked        Boolean? @default(false)
  sticky        Int?     @default(0)

  @@index([author_id], map: "index_messages_on_author_id")
  @@index([created_on], map: "index_messages_on_created_on")
  @@index([last_reply_id], map: "index_messages_on_last_reply_id")
  @@index([board_id], map: "messages_board_id")
  @@index([parent_id], map: "messages_parent_id")
}

model news {
  id             Int       @id @default(autoincrement())
  project_id     Int?
  title          String?   @db.VarChar(60)
  summary        String?   @db.VarChar(255)
  description    String?   @db.Text
  author_id      Int       @default(0)
  created_on     DateTime? @db.DateTime(0)
  comments_count Int       @default(0)

  @@index([author_id], map: "index_news_on_author_id")
  @@index([created_on], map: "index_news_on_created_on")
  @@index([project_id], map: "news_project_id")
}

model open_id_authentication_associations {
  id         Int     @id @default(autoincrement())
  issued     Int?
  lifetime   Int?
  handle     String? @db.VarChar(255)
  assoc_type String? @db.VarChar(255)
  server_url Bytes?  @db.Blob
  secret     Bytes?  @db.Blob
}

model open_id_authentication_nonces {
  id         Int     @id @default(autoincrement())
  timestamp  Int
  server_url String? @db.VarChar(255)
  salt       String? @db.VarChar(255)
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model overdue_datas {
  issues_id    Int?
  kind         String? @db.VarChar(100)
  subject      String? @db.VarChar(100)
  status       String? @db.VarChar(100)
  user_id      Int?
  user_fname   String? @db.VarChar(100)
  user_lname   String? @db.VarChar(100)
  create_time  String? @db.VarChar(100)
  start_time   String? @db.VarChar(100)
  due_date     String? @db.VarChar(100)
  project_name String? @db.VarChar(100)

  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model overdue_datas_project {
  project_name  String? @db.VarChar(100)
  overdue_sum   Int?
  bitech_sum    Int?
  other_company Int?
  unrecognized  Int?
  esw           Int?
  est           Int?
  ese           Int?
  tef           Int?
  ehw           Int?
  epm           Int?
  hmi           Int?

  @@ignore
}

model plugin_project_custom_settings {
  id          Int     @id @default(autoincrement())
  project_id  Int?
  tracker_id  Int?
  status_id   Int?
  page        String? @db.VarChar(255)
  msg         String? @db.Text
  custom_type String? @db.VarChar(255)
}

model project_alias {
  id    Int    @id @default(autoincrement())
  name  String @default("") @db.Char(20)
  alias String @default("") @db.Char(20)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model projects {
  id                     Int       @id @default(autoincrement())
  name                   String?   @db.VarChar(255)
  description            String?   @db.Text
  homepage               String?   @db.VarChar(255)
  is_public              Boolean   @default(true)
  parent_id              Int?
  created_on             DateTime? @db.DateTime(0)
  updated_on             DateTime? @db.DateTime(0)
  identifier             String?   @db.VarChar(255)
  status                 Int       @default(1)
  lft                    Int?
  rgt                    Int?
  inherit_members        Boolean   @default(false)
  default_version_id     Int?
  default_assigned_to_id Int?

  @@index([lft], map: "index_projects_on_lft")
  @@index([rgt], map: "index_projects_on_rgt")
}

model projects_trackers {
  project_id Int @default(0)
  tracker_id Int @default(0)

  @@unique([project_id, tracker_id], map: "projects_trackers_unique")
  @@index([project_id], map: "projects_trackers_project_id")
}

model queries {
  id            Int     @id @default(autoincrement())
  project_id    Int?
  name          String? @db.VarChar(255)
  filters       String? @db.Text
  user_id       Int     @default(0)
  column_names  String? @db.Text
  sort_criteria String? @db.Text
  group_by      String? @db.VarChar(255)
  type          String? @db.VarChar(255)
  visibility    Int?    @default(0)
  options       String? @db.Text

  @@index([project_id], map: "index_queries_on_project_id")
  @@index([user_id], map: "index_queries_on_user_id")
}

model queries_roles {
  query_id Int
  role_id  Int

  @@unique([query_id, role_id], map: "queries_roles_ids")
}

model repositories {
  id            Int       @id @default(autoincrement())
  project_id    Int       @default(0)
  url           String?   @db.VarChar(255)
  login         String?   @db.VarChar(60)
  password      String?   @db.VarChar(255)
  root_url      String?   @db.VarChar(255)
  type          String?   @db.VarChar(255)
  path_encoding String?   @db.VarChar(64)
  log_encoding  String?   @db.VarChar(64)
  extra_info    String?   @db.LongText
  identifier    String?   @db.VarChar(255)
  is_default    Boolean?  @default(false)
  created_on    DateTime? @db.DateTime(0)

  @@index([project_id], map: "index_repositories_on_project_id")
}

model roles {
  id                      Int      @id @default(autoincrement())
  name                    String?  @db.VarChar(30)
  position                Int?
  assignable              Boolean? @default(true)
  builtin                 Int      @default(0)
  permissions             String?  @db.Text
  issues_visibility       String?  @db.VarChar(30)
  users_visibility        String?  @db.VarChar(30)
  time_entries_visibility String?  @db.VarChar(30)
  all_roles_managed       Boolean  @default(true)
  settings                String?  @db.Text
}

model roles_managed_roles {
  role_id         Int
  managed_role_id Int

  @@unique([role_id, managed_role_id], map: "index_roles_managed_roles_on_role_id_and_managed_role_id")
}

model schema_migrations {
  version String @unique(map: "unique_schema_migrations") @db.VarChar(255)
}

model settings {
  id         Int       @id @default(autoincrement())
  name       String?   @db.VarChar(255)
  value      String?   @db.Text
  updated_on DateTime? @db.DateTime(0)

  @@index([name(length: 191)], map: "index_settings_on_name")
}

model taggings {
  id            Int       @id @default(autoincrement())
  tag_id        Int?
  taggable_id   Int?
  taggable_type String?   @db.VarChar(255)
  created_at    DateTime? @db.DateTime(0)

  @@index([tag_id], map: "index_taggings_on_tag_id")
  @@index([taggable_id, taggable_type(length: 191)], map: "index_taggings_on_taggable_id_and_taggable_type")
}

model tags {
  id   Int     @id @default(autoincrement())
  name String? @db.VarChar(255)
}

model time_entries {
  id          Int      @id @default(autoincrement())
  project_id  Int
  user_id     Int
  issue_id    Int?
  hours       Float    @db.Float
  comments    String?  @db.VarChar(1024)
  activity_id Int
  spent_on    DateTime @db.Date
  tyear       Int
  tmonth      Int
  tweek       Int
  created_on  DateTime @db.DateTime(0)
  updated_on  DateTime @db.DateTime(0)

  @@index([activity_id], map: "index_time_entries_on_activity_id")
  @@index([created_on], map: "index_time_entries_on_created_on")
  @@index([user_id], map: "index_time_entries_on_user_id")
  @@index([issue_id], map: "time_entries_issue_id")
  @@index([project_id], map: "time_entries_project_id")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model tmp {
  id      BigInt @default(0)
  percent BigInt @default(0)

  @@ignore
}

model tokens {
  id         Int       @id @default(autoincrement())
  user_id    Int       @default(0)
  action     String?   @db.VarChar(30)
  value      String?   @unique(map: "tokens_value") @db.VarChar(40)
  created_on DateTime  @db.DateTime(0)
  updated_on DateTime? @db.DateTime(0)

  @@index([user_id], map: "index_tokens_on_user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model trackers {
  id                Int     @id @default(autoincrement())
  name              String? @db.VarChar(30)
  is_in_chlog       Boolean @default(false)
  position          Int?
  is_in_roadmap     Boolean @default(true)
  fields_bits       Int?    @default(0)
  default_status_id Int?
}

model update_in_progresses {
  id         Int       @id @default(autoincrement())
  user_id    Int
  quote_char String?   @db.VarChar(8)
  col_sep    String?   @db.VarChar(8)
  encoding   String?   @db.VarChar(64)
  created    DateTime? @db.DateTime(0)
  csv_data   Bytes?    @db.MediumBlob
}

model user_preferences {
  id        Int      @id @default(autoincrement())
  user_id   Int      @default(0)
  others    String?  @db.Text
  hide_mail Boolean? @default(true)
  time_zone String?  @db.VarChar(255)

  @@index([user_id], map: "index_user_preferences_on_user_id")
}

model users {
  id                 Int       @id @default(autoincrement())
  login              String?   @db.VarChar(255)
  hashed_password    String?   @db.VarChar(40)
  firstname          String?   @db.VarChar(30)
  lastname           String?   @db.VarChar(255)
  admin              Boolean   @default(false)
  status             Int       @default(1)
  last_login_on      DateTime? @db.DateTime(0)
  language           String?   @db.VarChar(5)
  auth_source_id     Int?
  created_on         DateTime? @db.DateTime(0)
  updated_on         DateTime? @db.DateTime(0)
  type               String?   @db.VarChar(255)
  identity_url       String?   @db.VarChar(255)
  mail_notification  String?   @db.VarChar(255)
  salt               String?   @db.VarChar(64)
  must_change_passwd Boolean   @default(false)
  passwd_changed_on  DateTime? @db.DateTime(0)

  @@index([auth_source_id], map: "index_users_on_auth_source_id")
  @@index([id, type(length: 191)], map: "index_users_on_id_and_type")
  @@index([type(length: 191)], map: "index_users_on_type")
}

model versions {
  id              Int       @id @default(autoincrement())
  project_id      Int       @default(0)
  name            String?   @db.VarChar(255)
  description     String?   @db.VarChar(255)
  effective_date  DateTime? @db.Date
  created_on      DateTime? @db.DateTime(0)
  updated_on      DateTime? @db.DateTime(0)
  wiki_page_title String?   @db.VarChar(255)
  status          String?   @db.VarChar(255)
  sharing         String?   @db.VarChar(255)

  @@index([sharing(length: 191)], map: "index_versions_on_sharing")
  @@index([project_id], map: "versions_project_id")
}

model watchers {
  id             Int     @id @default(autoincrement())
  watchable_type String? @db.VarChar(255)
  watchable_id   Int     @default(0)
  user_id        Int?

  @@index([user_id], map: "index_watchers_on_user_id")
  @@index([watchable_id, watchable_type(length: 191)], map: "index_watchers_on_watchable_id_and_watchable_type")
  @@index([user_id, watchable_type(length: 191)], map: "watchers_user_id_type")
}

model wiki_content_versions {
  id              Int      @id @default(autoincrement())
  wiki_content_id Int
  page_id         Int
  author_id       Int?
  data            Bytes?
  compression     String?  @db.VarChar(6)
  comments        String?  @db.VarChar(1024)
  updated_on      DateTime @db.DateTime(0)
  version         Int

  @@index([updated_on], map: "index_wiki_content_versions_on_updated_on")
  @@index([wiki_content_id], map: "wiki_content_versions_wcid")
}

model wiki_contents {
  id         Int      @id @default(autoincrement())
  page_id    Int
  author_id  Int?
  text       String?  @db.LongText
  comments   String?  @db.VarChar(1024)
  updated_on DateTime @db.DateTime(0)
  version    Int

  @@index([author_id], map: "index_wiki_contents_on_author_id")
  @@index([page_id], map: "wiki_contents_page_id")
}

model wiki_pages {
  id         Int      @id @default(autoincrement())
  wiki_id    Int
  title      String?  @db.VarChar(255)
  created_on DateTime @db.DateTime(0)
  protected  Boolean  @default(false)
  parent_id  Int?

  @@index([parent_id], map: "index_wiki_pages_on_parent_id")
  @@index([wiki_id], map: "index_wiki_pages_on_wiki_id")
  @@index([wiki_id, title(length: 191)], map: "wiki_pages_wiki_id_title")
}

model wiki_redirects {
  id                   Int      @id @default(autoincrement())
  wiki_id              Int
  title                String?  @db.VarChar(255)
  redirects_to         String?  @db.VarChar(255)
  created_on           DateTime @db.DateTime(0)
  redirects_to_wiki_id Int

  @@index([wiki_id], map: "index_wiki_redirects_on_wiki_id")
  @@index([wiki_id, title(length: 191)], map: "wiki_redirects_wiki_id_title")
}

model wikis {
  id         Int     @id @default(autoincrement())
  project_id Int
  start_page String? @db.VarChar(255)
  status     Int     @default(1)

  @@index([project_id], map: "wikis_project_id")
}

model workflows {
  id            Int     @id @default(autoincrement())
  tracker_id    Int     @default(0)
  old_status_id Int     @default(0)
  new_status_id Int     @default(0)
  role_id       Int     @default(0)
  assignee      Boolean @default(false)
  author        Boolean @default(false)
  type          String? @db.VarChar(30)
  field_name    String? @db.VarChar(30)
  rule          String? @db.VarChar(30)

  @@index([new_status_id], map: "index_workflows_on_new_status_id")
  @@index([old_status_id], map: "index_workflows_on_old_status_id")
  @@index([role_id], map: "index_workflows_on_role_id")
  @@index([tracker_id], map: "index_workflows_on_tracker_id")
  @@index([role_id, tracker_id, old_status_id], map: "wkfs_role_tracker_old_status")
}
