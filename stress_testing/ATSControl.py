import subprocess
import pyautogui
import time
from utils import singleton

@singleton
class ATSRunner:
    def __init__(self, exe_path=r"D:\Software\ATS_1.0.6.2_Update\ATS_1.0.6.3ESW\Debug\AutomationTestStation.exe", excel_path=r"C:\Users\<USER>\Desktop\Chery_压测_SWP1.00_ATS-V1.0_Q.xlsm"):
        self.exe_path = exe_path
        self.excel_path = excel_path
        self.process_ats = None

    def run_ats(self):
        try:
            # 缩小所有窗口
            pyautogui.hotkey('win', 'd')
            time.sleep(2)

            # 启动ATS程序
            ats_arguments = [self.excel_path, "Sheet1"]
            self.process_ats = subprocess.Popen(
                [self.exe_path] + ats_arguments,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )
            time.sleep(4)
            # 模拟按下 'y' 键
            pyautogui.press('y')
            time.sleep(4)
            print("ATS started successfully.")
        except Exception as e:
            print(f"[Error]: {e}")

    def stop_ats(self):
        try:
            if self.process_ats:
                # 终止ATS程序
                self.process_ats.terminate()
                self.process_ats.wait()
                time.sleep(5)
                self.process_ats = None
                print("ATS stopped successfully.")
            else:
                print("ATS is not running.")
        except Exception as e:
            print(f"[Error]: {e}")

# 示例使用

