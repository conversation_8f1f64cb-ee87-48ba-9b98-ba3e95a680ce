from datetime import datetime
import shutil
import subprocess
import requests
from bs4 import BeautifulSoup
import zipfile
import os
import argparse

url = "http://*************/files/release/chery/t19c/mcu/daily/"


def unzip_pkg(zip_path: str):
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(zip_path + '/..')

    print("ZIP文件已成功解压到:", zip_path)


def copy_latest_file(source_folder, destination_folder):
    # 获取源文件夹中所有文件的路径
    os.makedirs(destination_folder, exist_ok=True)
    file_paths = [os.path.join(source_folder, filename)
                  for filename in os.listdir(source_folder)]

    # 根据文件的修改时间进行排序
    file_paths.sort(key=lambda x: os.path.getmtime(x))

    # 获取最新修改的文件路径
    latest_file = file_paths[-1]
    print(f"当前最新包：{latest_file}")

    # 复制文件到目标文件夹
    shutil.copy2(latest_file, fr'{destination_folder}/mcu_build_target.zip')


def get_cloud_package(repoPath: str):
    # 发送HTTP请求
    response = requests.get(url)

    # 解析HTML响应
    soup = BeautifulSoup(response.text, "html.parser")

    # 获取所有链接
    links = soup.find_all("a")

    # 保存最新包的信息
    latest_package = None
    latest_time = None

    # 遍历链接并找到最新包
    for link in links:
        href = link.get("href")
        if href.endswith(".zip"):
            # 获取包的最后更新时间
            last_modified_ele: str = link.next_sibling

            last_modified = datetime.strptime(
                ' '.join(last_modified_ele.split()[:-1]), '%d-%b-%Y %H:%M')
            if latest_time is None or last_modified > latest_time:
                latest_package = href
                latest_time = last_modified

    # 输出最新包的信息
    if latest_package is not None:
        print("最新包:", latest_package)
        print("最后更新时间:", latest_time)
    else:
        print("未找到任何包")

    response = requests.get(url + latest_package)
    print(url + latest_package)
    compile_path = f'{repoPath}/../mcu_build_target'
    os.makedirs(compile_path, exist_ok=True)
    if response.status_code == 200:
        with open(f'{compile_path}/mcu_build_target.zip', "wb") as file:
            file.write(response.content)
        print("包已成功下载到:", compile_path)
    else:
        print("下载包时出错:", response.status_code)


def get_latest_pkg(repoPath: str, use_public: bool):
    compile_path = f'{repoPath}/../mcu_build_target'
    result = subprocess.run(
        'git pull origin develop', cwd=repoPath, shell=True)
    if result.returncode == 0:
        print("Update Repo Successful!")
    else:
        print("Update Repo Failed!")
    shutil.rmtree(f'{repoPath}_build_target', ignore_errors=True)
    if use_public:
        copy_latest_file(
            source_folder=r'Y:\ESW\10_SUP\09.Integration\Chery\T19C\mcu\Daily', destination_folder=compile_path)
    else:
        get_cloud_package(repoPath=repoPath)

    unzip_pkg(zip_path=f'{compile_path}/mcu_build_target.zip')


def auto_flash(repoPath: str, flash_ext: bool):
    # compile
    # %JFlashPath% -openprjE:\01_DaliyWork\00_ESW\02_ESW2\05_ESWTestBox\GWM_Testbox\CD569HL_cfg.jflash -open%cm0path% -merge%cm4path% -auto -exit
    with open(f'{repoPath}/tool/tviic_cm0.jflash', 'r+') as f:
        lines = f.readlines()
        modified_lines = []
        for line in lines:
            if 'CurrentFile' in line:
                line = line.split('=')[0] + \
                    f'= "{repoPath}_build_target/RELWITHDEBINFO/Chery_T19C_cm0plus.srec"\n'
            modified_lines.append(line)
        f.seek(0)  # 将文件指针移回文件开头
        f.writelines(modified_lines)
        f.truncate()  # 截断文件，删除多余的内容

    # ..\Jlink_Flash_Tool\JFlash.exe -openprj%~dp0\tviic_with_gdflash.jflash -open%~dp0\..\..\cgi\AssetLibraries\output\AssetLibCff_Traveo2_Target_Partition_Combined.bin,0x60000000 -auto -exit
    if flash_ext:
        result = subprocess.run(
            r'tool\JLink_Flash_Tool\JFlash.exe -openprjtool\tviic_with_gdflash.jflash -openhmi\Assets\AssetLibCff_Traveo2_Target_Partition_Combined.bin,0x60000000 -auto -exit', cwd=repoPath, shell=True)
        if result.returncode == 0:
            print('Ext Flash Success')
        else:
            print('Ext Flash Maybe Failed')

    result = subprocess.run(
        r'tool\JLink_Flash_Tool\JFlash.exe -openprjtool\tviic_cm0.jflash -open../mcu_build_target/RELWITHDEBINFO/Chery_T19C_cm0plus.srec -merge../mcu_build_target/RELWITHDEBINFO/Chery_T19C_cm7.srec -auto -exit', cwd=repoPath, shell=True)
    if result.returncode == 0:
        print('Flash Success')
    else:
        print(result.stdout)
        print('Flash Failed')


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='AutoFlash')

    parser.add_argument('--repo-path', help='仓库路径', default='.')
    parser.add_argument('--enable-cloud', help='启用云编译',
                        default=False, action='store_true')
    parser.add_argument('--disable-flash-ext', help='不刷Ext Flash',
                        default=False, action='store_true')
    parser.add_argument('--use-public', help='公共盘编译',
                        default=False, action='store_true')
    args = parser.parse_args()
    enable_cloud = args.enable_cloud
    repo_path = args.repo_path
    disable_flash_ext = args.disable_flash_ext
    use_public = args.use_public

    if enable_cloud or use_public:
        get_latest_pkg(repoPath=repo_path, use_public=use_public)
    auto_flash(repoPath=repo_path, flash_ext=not disable_flash_ext)
