# Import CANoe module
from py_canoe import CANoe
from time import sleep
from datetime import datetime
import json
from utils import singleton, deprecated


@singleton
# Python 中的“CanoeRunner”类初始化 CANoe 配置文件并与之交互、启动和停止测量、读取 CAN 总线统计数据并记录与 CAN 信号相关的错误消息。
class CanoeRunner:
    def __init__(self, cfgPath: str):
        """
        此 Python 函数初始化各种属性并打开由“cfgPath”参数指定的 CANoe 配置文件。

        @param cfgPath “__init__”方法中的“cfgPath”参数是一个字符串，表示配置文件的路径。该路径用于打开“CANoe”对象中的 CANoe 配置文件。
        """

        self.pre_standard_total = -1
        self.can_logs = []
        self.error_logs = []
        self.is_stop = False
        self.canoe = CANoe()
        self.can_down_log = []
        self.canoe.open(prompt_user=False,
                        canoe_cfg=f'{cfgPath}'
                        )

    def start_measurement(self, test_mode: str = 'stress'):
        """
        该函数在 CANoe 环境中启动测量并设置多个环境变量值。
        @note 通过设置环境变量来控制CANoe Panel中选项的开关，从而实现全模块负载
        """
        sleep(1)
        self.canoe.start_measurement()
        # if test_mode == 'stress':
        #     self.canoe.set_environment_variable_value('ALL_Telltales', 1)
        #     self.canoe.set_environment_variable_value('Gauge', 1)
        #     self.canoe.set_environment_variable_value('RRM', 1)
        #     self.canoe.set_environment_variable_value('button', 1)
        #     self.canoe.set_environment_variable_value('PowerChange', 1)
        #     self.canoe.set_environment_variable_value('Env_All_Warning', 1)
        #     self.canoe.set_environment_variable_value('Env_CH2', 1)
        #     self.canoe.set_environment_variable_value('RadioBlue', 1)
        # else:
        #     self.canoe.set_environment_variable_value('Env_BothLightBlink', 1)
        #     self.canoe.set_environment_variable_value('Gauge', 1)

    def stop_measurement(self):
        """
        `stop_measurement` 函数停止 Canoe 对象中的测量并等待 1 秒。
        """
        self.canoe.stop_measurement()
        sleep(1)

    def quit_canoe(self):
        """
        该函数旨在退出CANoe程序。
        """
        self.canoe.quit()

    def read_can_statistics(self, chanel: int = 1):
        """
        此函数使用 Canoe 工具读取指定通道的 CAN 总线统计数据。

        @param chanel “read_can_statistics”方法中的参数“chanel”用于指定要检索 CAN 总线统计信息的通道号。它的默认值为
        1，这意味着如果调用该方法时没有提供通道号，则默认为通道 1。

        @return 使用“canoe”对象的“get_can_bus_statistics”方法获取指定通道的 CAN 总线统计信息。
        """
        return self.canoe.get_can_bus_statistics(chanel)

    @deprecated
    def start_catch_log(self):
        """
        该功能连续读取 CAN 统计数据，并在信号关闭时记录错误消息。
        \deprecated
        """
        while not self.is_stop:
            sleep(1)
            log = self.read_can_statistics()
            if log['standard_total'] == self.pre_standard_total:
                current_time = datetime.datetime.now().strftime("%H:%M:%S")
                print(f'{current_time}: Can signal is down!\n')
                self.error_logs.append(
                    {'time': f'{current_time}', 'message': 'Can signal is down!'})
            self.pre_standard_total = log['standard_total']

    def write_logs(self, log_path: str):
        """
        函数 write_logs 将错误日志写入指定路径的 JSON 文件。

        @param log_path write_logs 方法中的 log_path 参数是一个字符串，表示日志文件的保存路径。它用于指定将创建或更新日志文件的目录位置。
        """

        if self.error_logs:
            with open(f'{log_path}/error_log.json', "w") as f:
                f.write(json.dumps(self.error_logs, indent=2))

    @deprecated
    def end_catch_log(self, log_path: str):
        """
        函数“end_catch_log”停止测量，将日志写入指定路径，并设置一个标志来指示进程已停止。

        @param log_path end_catch_log 方法中的 log_path
        参数是一个字符串，表示日志将写入的文件路径。该方法停止测量过程，将日志写入指定的文件路径，并将标志“is_stop”设置为True。
        \deprecated
        """
        self.canoe.stop_measurement()
        self.write_logs(log_path)
        self.is_stop = True

    def sleep_and_judge_is_down(self, mode: int, timer: int, interval: int = 1, channel: int = 1):
        """
        此 Python 函数会休眠指定的时间间隔，同时检查 CAN 信号是否未传输。

        @param mode `sleep_and_judge_is_down`方法中的`mode`参数用于指定操作模式。它是一个整数值，确定方法执行期间要采取的特定行为或操作。
        “mode”参数的确切含义和可能值将
        @param timer “sleep_and_judge_is_down”方法中的“timer”参数表示函数将休眠指定时间间隔的次数，然后检查 CAN
        信号是否已关闭。它决定了循环中将执行多少次睡眠和检查迭代。
        @param interval “sleep_and_judge_is_down”方法中的“interval”参数指定代码在检查条件之前休眠的时间间隔（以秒为单位）。默认情况下，它设置为 1
        秒，但您可以通过在调用方法时提供不同的值来自定义它
        @param channel
        `sleep_and_judge_is_down`方法中的`channel`参数用于指定检查CAN信号状态的通道号。可选参数，默认值为1。调用方法时如果不提供值，则默认检查
        """
        # 休眠指定事件的同时判断can信号是否停发（检测卡死）
        for _ in range(timer):
            sleep(interval)
            self.is_down(channel=channel, mode=mode)

    def is_down(self, channel=1, mode=1):
        """
        该功能通过比较来自 CAN 总线通道的当前和之前的总标准值来检查系统是否卡住。

        @param channel “is_down”方法中的“channel”参数用于指定要检查其是否已关闭的通道号。如果调用该方法时未提供，则将其设置为默认值 1。该参数用于检索与指定 CAN
        相关的统计信息
        @param mode “is_down”方法中的“mode”参数用于指定调用该方法的操作模式或上下文。它是一个可选参数，默认值为
        1。此参数有助于根据不同的模式或方法提供附加信息或自定义方法。
        """

        # 判断是否卡死
        current_standard_total = self.canoe.get_can_bus_statistics(channel)[
            'standard_total']
        if current_standard_total == self.pre_standard_total:
            self.can_down_log.append({
                "time": datetime.now().strftime("%H:%M:%S"),
                "current_can_total": current_standard_total,
                "pre_can_total": self.pre_standard_total,
                "mode": mode
            })
        self.pre_standard_total = current_standard_total

    def write_can_down_log(self, log_path: str):
        """
        此 Python 函数将字典写入指定路径的 JSON 文件。

        @param log_path `log_path`
        参数是一个字符串，表示日志文件的保存路径。在提供的代码片段中，“write_can_down_log”方法将对象的“can_down_log”属性写入位于“log_path +
        ”/can_down_log 的 JSON 文件中
        """
        with open(log_path + '/can_down_log.json', 'w', encoding='utf-8') as f:
            json.dump(self.can_down_log, f, ensure_ascii=False, indent=2)


if __name__ == '__main__':
    canoe_runner = CanoeRunner(
        cfgPath=r'D:\T19C_StrssTest_0.2\T19C_StrssTest_0.2\Chery T19C simulation11.0.cfg')
    canoe_runner.start_measurement(test_mode='normal')
    # canoe_runner.canoe.set_environment_variable_value('ALL_Telltales', 1)
    # canoe_runner.canoe.set_environment_variable_value('Gauge', 1)
    # canoe_runner.canoe.set_environment_variable_value('RRM', 1)
    # canoe_runner.canoe.set_environment_variable_value('button', 1)
    # canoe_runner.canoe.set_environment_variable_value('PowerChange', 1)
    # canoe_runner.canoe.set_environment_variable_value('Env_All_Warning', 1)
    # canoe_runner.canoe.set_environment_variable_value('Env_CH2', 1)
    # canoe_runner.canoe.set_environment_variable_value('RadioBlue', 1)
    # canoe_runner.canoe.set_environment_variable_value('Env_BothLightBlink', 1)
