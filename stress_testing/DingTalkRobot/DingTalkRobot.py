import json
from DingTalkRobot.config import robots, conversations
import requests
from alibabacloud_dingtalk.oauth2_1_0.client import Client as dingtalkoauthClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.oauth2_1_0 import models as dingtalk_auth_model
from alibabacloud_tea_util.client import Client as UtilClient
from alibabacloud_dingtalk.robot_1_0 import models as dingtalkrobot_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_dingtalk.robot_1_0.client import Client as dingtalkrobot_Client
from DingTalkRobot.message_template import create_message_template


class DingTalkRobot:
    host = "api.dingtalk.com"

    def __init__(self, robot_name: str, conversation_name: str) -> None:
        # params
        self.robot_name = robot_name
        self.client = self.create_client()
        # 从配置中查询具体的robot
        self.robot = next(
            filter(lambda item: item['name'] == self.robot_name, robots))
        self.conversation = next(
            filter(lambda item: item['name'] == conversation_name, conversations))
        if not self.robot:
            raise Exception(
                "Can not find robot!!!Please check your config file!!!")
        if not self.conversation:
            raise Exception(
                "Can not find conversation!!!Please check your config file!!!")

    def create_client(self) -> dict:
        """
        使用 Token 初始化账号Client
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'

        return {
            'auth': dingtalkoauthClient(config),
            'robot': dingtalkrobot_Client(config)
        }

    def __get_access_token(self):
        try:
            response = self.client['auth'].get_access_token(dingtalk_auth_model.GetAccessTokenRequest(
                app_key=self.robot['AppKey'],
                app_secret=self.robot['AppSecret']
            ))
            self.access_token = response.body.access_token
        except Exception as err:
            if not UtilClient.empty(err.code) and not UtilClient.empty(err.message):
                # err 中含有 code 和 message 属性，可帮助开发定位问题
                pass

    def send_markdown_message(self, title: str, message: str):
        """
        The function `send_markdown_message` sends a markdown message using the DingTalk robot API.
        
        :param title: The title parameter is a string that represents the title of the message. It is
        used to provide a brief summary or heading for the message
        :type title: str
        :param message: The `message` parameter is a string that represents the content of the message
        you want to send. It can be in Markdown format, which allows you to format the text with
        headings, lists, links, and other formatting options
        :type message: str
        """
        # init request
        self.__get_access_token()
        org_group_send_headers = dingtalkrobot_models.OrgGroupSendHeaders()
        org_group_send_headers.x_acs_dingtalk_access_token = self.access_token
        org_group_send_request = dingtalkrobot_models.OrgGroupSendRequest(
            msg_param=json.dumps({
                "title": title,
                "text": message
            }),
            msg_key="sampleMarkdown",
            robot_code=self.robot['robotCode'],
            open_conversation_id=self.conversation['conversation_id'],
        )
        try:
            self.client['robot'].org_group_send_with_options(
                org_group_send_request, org_group_send_headers, util_models.RuntimeOptions())
        except Exception as err:
            if not UtilClient.empty(err.code) and not UtilClient.empty(err.message):
                # err 中含有 code 和 message 属性，可帮助开发定位问题
                pass

    def send_file_message(self, file_type: str, file_name: str, file_path: str) -> None:
        """
        The function `send_file_message` sends a file message using the DingTalk robot API.
        
        :param file_type: The `file_type` parameter is a string that represents the type of the file
        being sent. It could be any valid file type such as "pdf", "docx", "xlsx", etc
        :type file_type: str
        :param file_name: The `file_name` parameter is the name of the file that you want to send. It
        should be a string value
        :type file_name: str
        :param file_path: The `file_path` parameter is the path to the file that you want to send. It
        should be a string representing the file's location on your system
        :type file_path: str
        """
        # init request
        self.__get_access_token()
        media_id = self.__upload_file(file_path=file_path)
        org_group_send_headers = dingtalkrobot_models.OrgGroupSendHeaders()
        org_group_send_headers.x_acs_dingtalk_access_token = self.access_token
        org_group_send_request = dingtalkrobot_models.OrgGroupSendRequest(
            msg_param=json.dumps({
                "mediaId": media_id,
                "fileName": file_name,
                "fileType": file_type
            }),
            msg_key="sampleFile",
            robot_code=self.robot['robotCode'],
            open_conversation_id=self.conversation['conversation_id'],
        )
        try:
            self.client['robot'].org_group_send_with_options(
                org_group_send_request, org_group_send_headers, util_models.RuntimeOptions())
        except Exception as err:
            print(err)
            if not UtilClient.empty(err.code) and not UtilClient.empty(err.message):
                # err 中含有 code 和 message 属性，可帮助开发定位问题
                pass

    def __upload_file(self, file_path: str) -> dict:
        """
        The function uploads a file to DingTalk and returns the media ID of the uploaded file.
        
        :param file_path: The `file_path` parameter is a string that represents the path to the file
        that you want to upload. It should be the absolute path to the file on your local machine
        :type file_path: str
        :return: a dictionary. Specifically, it is returning the value associated with the key
        'media_id' in the JSON response from the API call.
        """
        # init request
        self.__get_access_token()
        response = requests.post('https://oapi.dingtalk.com/media/upload', params={
            'access_token': self.access_token
        }, data={
            "type": "file",
        }, files={
            "media": open(file_path, "rb")
        })
        print(response.json())
        return response.json()['media_id']


if __name__ == "__main__":
    robot = DingTalkRobot("EQ100", conversation_name="压力测试通知群")
    params = []
    for stage in [1, 2, 3]:
        result = {
            f"stage{stage}_runtime": '1h',
            f"stage{stage}_expected_reset_count": 5,
            f"stage{stage}_reset_count": 5,
            f"stage{stage}_cpu_loading": 80,
            f"stage{stage}_max_cpu_loading": 80,
            f"stage{stage}_ddr_usage": 30,
            f"stage{stage}_max_ddr_usage": 30,
            f"stage{stage}_black_screen_count": 0,
            f"stage{stage}_error_count": 3,
            f"stage{stage}_down_count": 0,
            f"stage{stage}_cpu_max": 78,
            f"stage{stage}_ddr_max": 59,
            f"stage{stage}_ddr_threshold_value": 60,
            f"stage{stage}_cpu_threshold_value": 80,
            f"stage{stage}_cpu_average": 66,
            f"stage{stage}_ddr_average": 55,
        }
        params.append(result)
    params_dict = {k: v for d in params for k, v in d.items()} | {
        # "OEM": OEM,
        "project": "EQ100",
        "contacts": "",
        "test_date": "2023-10-10",
        "final_result": True,
        "build_date": "2023/10/10",
        "sw_version": "SWP11111",
        "cause": "超时"
    }
    message = create_message_template(params_dict=params_dict)
    robot.send_markdown_message(title="EQ100自动化压力测试测试报告", message=message)
    robot.send_file_message(file_name="report.pdf",
                            file_path="Y:/ESW/10_SUP/04.AutoTest/SGMW/EQ100/2023-10-20-155152/output/report.pdf", file_type="pdf")
