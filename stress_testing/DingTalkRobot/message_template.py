# 压测报告模板
def create_message_template(params_dict: dict) -> str:
    # sourcery skip: low-code-quality
    return f"""
## {params_dict['project']}压力测试测试报告（简洁版）
                                
<hr />
                                
### <font color=#1890ff>基本信息</font>
                                
<hr />
                                
- 测试结果: {'<font color=green>测试通过</font>' if params_dict['final_result'] == 'PASS' else '<font color=red>测试失败</font>'}
- 测试时间: {params_dict['test_date']}
- 构建日期: {params_dict['compile_date']}
- 软件版本: {params_dict['sw_version']}
{f"- 失败原因: {params_dict['cause']}" if not params_dict['final_result'] == 'PASS' else ""}
{f"- 风险项(Warning):  {params_dict['warning']}" if params_dict['warning'] else ""}
- 测试报告地址: {params_dict['output_path']}
- 压测视频地址: {params_dict['video_path']}

<br />

### <font color=#1890ff>测试目的</font>

<hr />

系统压力测试，对测试结果进行分析。判断运行过程中待测设备是否发生异常复位，DDR使用率和CPU Loading是否超出阈值，是否有ErrorLog产生，是否出现can停发。

<br />

### <font color=#1890ff>测试步骤</font>

<hr />

1. 指示灯：EPSSteeringAngleCalibrationSts=1，CDPFailSts_2C0=1，等多个指示灯
1. 表头：车速随机切换，转速随机切换
1. 主题：主题1/2/3切换
1. 按键：60ms切换一次左键PressLeft_CAN=1
1. 报警：BCM_4_BrakelampFaultSts=1;BCM_4_DriverDoorSts=1;等多个弹窗报警。

> 指示灯和文字报警具体指哪些还没有仔细整理
"""

# """
# ### <font color=#1890ff>压测流程</font>

# <hr />

# 1. 第一阶段 - 周期性休眠唤醒压力测试

# <br />

# | 测试项  | 预期行为 | 实际行为  | 测试结果 |
# | ------------ | ------- | ----------- | :----------- |
# | 系统复位 | 启动{params_dict['stage1_expected_reset_count']}次 | 启动{params_dict['stage1_reset_count']}次 | {"<font color=green size=0>测试通过</font>" if params_dict['stage1_reset_count'] == params_dict['stage1_expected_reset_count'] else "<font color=red size=0>测试失败</font>"} |
# | CPU Loading | <{params_dict['stage1_cpu_threshold_value']}% | AVG:{params_dict['stage1_cpu_average']}%<br/>MAX:{params_dict['stage1_cpu_max']}% | {"<font color=green size=0>测试通过</font>" if params_dict['stage1_cpu_max'] < params_dict['stage1_cpu_threshold_value'] else "<font color=red size=0>测试失败</font>" }  |
# | DDR Usage | <{params_dict['stage1_ddr_threshold_value']}% | AVG:{params_dict['stage1_ddr_average']}%<br/>MAX:{params_dict['stage1_ddr_max']}% | {"<font color=green size=0>测试通过</font>" if params_dict['stage1_ddr_max'] < params_dict['stage1_ddr_threshold_value'] else "<font color=red size=0>测试失败</font>" }  |
# | Errorlog | 0个Error | {params_dict['stage1_error_count']}个Error | {"<font color=green size=0>测试通过</font>" if params_dict['stage1_error_count'] == 0 else "<font color=red size=0>测试失败</font>" }  |

# <br />

# 2. 第二阶段 - 高负载长时间压力测试

# <br />

# | 测试项  | 预期行为 | 实际行为  | 测试结果 |
# | ------------ | ------- | ----------- | :----------- |
# | 系统复位 | 启动1次 | 启动{params_dict['stage2_reset_count'] - params_dict['stage2_expected_reset_count'] + 1}次 | {"<font color=green size=0>测试通过</font>" if params_dict['stage2_reset_count'] - params_dict['stage2_expected_reset_count'] == 0 else "<font color=red size=0>测试失败</font>"} |
# | CPU Loading | <{params_dict['stage2_cpu_threshold_value']}% | AVG:{params_dict['stage2_cpu_average']}%<br/>MAX:{params_dict['stage2_cpu_max']}% | {"<font color=green size=0>测试通过</font>" if params_dict['stage2_cpu_max'] < params_dict['stage2_cpu_threshold_value'] else "<font color=red size=0>测试失败</font>" }  |
# | DDR Usage | <{params_dict['stage2_ddr_threshold_value']}% | AVG:{params_dict['stage2_ddr_average']}%<br/>MAX:{params_dict['stage2_ddr_max']}% | {"<font color=green size=0>测试通过</font>" if params_dict['stage2_ddr_max'] < params_dict['stage2_ddr_threshold_value'] else "<font color=red size=0>测试失败</font>" }  |
# | Errorlog | 0个Error | {params_dict['stage2_error_count']}个Error | {"<font color=green size=0>测试通过</font>" if params_dict['stage2_error_count'] == 0 else "<font color=red size=0>测试失败</font>" }  |

# <br />

# 3. 第三阶段 - KL30 周期性复位压力测试

# <br />

# | 测试项  | 预期行为 | 实际行为  | 测试结果 |
# | ------------ | ------- | ----------- | :----------- |
# | 系统复位 | 启动{params_dict['stage3_expected_reset_count']}次 | 启动{params_dict['stage3_reset_count']}次 | {"<font color=green size=0>测试通过</font>" if params_dict['stage3_reset_count'] == params_dict['stage3_expected_reset_count'] else "<font color=red size=0>测试失败</font>"} |
# | CPU Loading | <{params_dict['stage3_cpu_threshold_value']}% | AVG:{params_dict['stage3_cpu_average']}%<br/>MAX:{params_dict['stage3_cpu_max']}% | {"<font color=green size=0>测试通过</font>" if params_dict['stage3_cpu_max'] < params_dict['stage3_cpu_threshold_value'] else "<font color=red size=0>测试失败</font>" }  |
# | DDR Usage | <{params_dict['stage3_ddr_threshold_value']}% | AVG:{params_dict['stage3_ddr_average']}%<br/>MAX:{params_dict['stage3_ddr_max']}% | {"<font color=green size=0>测试通过</font>" if params_dict['stage3_ddr_max'] < params_dict['stage3_ddr_threshold_value'] else "<font color=red size=0>测试失败</font>" }  |
# | Errorlog | 0个Error | {params_dict['stage3_error_count']}个Error | {"<font color=green size=0>测试通过</font>" if params_dict['stage3_error_count'] == 0 else "<font color=red size=0>测试失败</font>" }  |

# """
