import json
import socket
from pyecharts import options
from pyecharts.charts import Bar, Line, Grid
from pyecharts.globals import CurrentConfig
from pyecharts.commons.utils import JsCode
import subprocess
import os

# 定义echarts服务器
# 代码片段中的 `CurrentConfig.ONLINE_HOST = "http://localhost:8000/assets/"` 行正在设置 Echarts 资产的在线主机 URL。此配置指定
# Echarts 库将在其中查找 JavaScript 文件、CSS 样式表以及渲染交互式图表所需的其他资源等资源的基本 URL。
CurrentConfig.ONLINE_HOST = "http://localhost:8000/assets/"


def renderChart(*, xaxis: str, width: str = '800px', height: str = '380px', chart_id='chart', yaxis_list: list[str], title: str, enable_rate: bool = True, chart_type: str, raw_data: list):
    """
    函数“renderChart”根据提供的数据和参数生成并渲染交互式图表，支持条形图和折线图类型。

    @param xaxis “renderChart”函数中的“xaxis”参数表示将在图表的 x 轴上显示的数据。它是必需参数，并且应该是与图表中的 x 轴数据相对应的字符串。
    @param width “renderChart”函数中的“width”参数指定图表的宽度。默认情况下，它设置为“800px”，但您可以通过在调用函数时提供不同的宽度来覆盖此默认值。
    @param height
    “renderChart”函数中的“height”参数指定将渲染的图表的高度。默认情况下，高度设置为“380px”，但您可以通过在调用函数时提供不同的高度值来覆盖此默认值。高度决定图表的高度
    @param chart_id “renderChart”函数中的“chart_id”参数用于指定渲染的 HTML 输出中图表元素的 ID。此 ID 可用于使用 JavaScript
    来设置图表样式或与图表交互。
    @param yaxis_list “renderChart”函数中的“yaxis_list”参数是一个字符串列表，表示图表中 y 轴的名称。列表中的每个字符串对应于将显示在图表上的特定 y 轴。
    @param title “renderChart”函数中的“title”参数用于指定将显示的图表的标题。它是必需参数，应该是描述图表内容或用途的字符串值。
    @param enable_rate “renderChart”函数中的“enable_rate”参数是一个布尔标志，用于确定是否启用图表中 y
    轴标签的速率格式。如果“enable_rate”设置为“True”，则 y 轴标签的格式将显示为后跟百分比的值
    @param chart_type
    “renderChart”函数中的“chart_type”参数指定要渲染的图表类型。它可以是条形图的“bar”或折线图的“line”。该函数根据该参数动态创建指定类型的图表并自定义其选项
    @param raw_data “renderChart”函数中的“raw_data”参数应该是一个包含字典的列表，其中每个字典代表一个数据点。每个字典都应具有与图表的 x 轴和 y 轴值相对应的键。

    @return 用于根据提供的参数（例如 x 轴数据、宽度、高度、图表 ID、y 轴列表、标题、启用率选项、图表类型和原始数据）渲染图表的嵌入代码。
    """

    if chart_type == 'bar':
        chart = Bar(
            init_opts=options.InitOpts(
                width=width, height=height, chart_id=chart_id),
        )
    else:
        chart = Line(
            init_opts=options.InitOpts(
                width=width, height=height, chart_id=chart_id),
        )

    chart.add_xaxis(xaxis_data=[xaxis_data[xaxis] for xaxis_data in raw_data])
    for yaxis in yaxis_list:
        chart.add_yaxis(yaxis, [yaxis_data[yaxis] for yaxis_data in raw_data])

    if chart_type == 'bar':
        chart.set_global_opts(
            title_opts=options.TitleOpts(title=title),
            yaxis_opts=options.AxisOpts(
                axislabel_opts=options.LabelOpts(formatter=JsCode(
                    "function(value) {return value + '%'}")) if enable_rate else None
            ),
        )
    elif chart_type == 'line':
        chart.set_global_opts(
            title_opts=options.TitleOpts(title=title),
            yaxis_opts=options.AxisOpts(
                axislabel_opts=options.LabelOpts(formatter=JsCode(
                    "function(value) {return value + '%'}")) if enable_rate else None,
                axisline_opts=options.AxisLineOpts(is_show=False)
            ),
            xaxis_opts=options.AxisOpts(
                type_="category", boundary_gap=False),
        )

    return chart.render_embed()


def is_port_in_use(port):
    """
    函数“is_port_in_use”检查给定端口是否已在本地计算机上使用。

    @param port
    “is_port_in_use”函数中的“port”参数是一个整数，表示要检查可用性的端口号。该函数尝试将套接字绑定到本地主机上的指定端口，以确定它是否已在使用中。如果端口可用，则该功能

    @return 如果端口已在使用，则函数“is_port_in_use”返回“True”；如果端口可供使用，则返回“False”。
    """
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('localhost', port))
            return False
        except socket.error:
            return True


def renderTaskDetail(*, report_path: str, stage: int) -> None:
    """
    此函数从 JSON 文件读取数据，根据指定阶段对其进行过滤，创建条形图可视化，并返回嵌入的图表。

    @param report_path `report_path` 参数是一个字符串，表示 `cycle_report.json` 文件所在目录的路径。该文件包含用于呈现任务详细信息图表的原始数据。
    @param stage “renderTaskDetail”函数中的“stage”参数表示您要渲染任务详细信息的任务的阶段。它用于根据指定阶段过滤“cycle_report.json”文件中的数据。

    @return 函数“renderTaskDetail”返回条形图的嵌入式渲染，该条形图根据提供的数据显示任务执行状态。
    """

    with open(f'{report_path}/cycle_report.json', 'r', encoding='utf-8') as f:
        raw_data = json.load(f)
    stage_raw_data = []
    task_detail_data = []
    stage_raw_data = [item for item in raw_data if item['mode'] == stage]
    for stage_data in stage_raw_data:
        task_detail_data.append(stage_data['task_detail'])
    # 设置图片大小
    init_opts = options.InitOpts(
        width='800px', height='450px', chart_id="chart")
    chart = Bar(init_opts=init_opts)
    lineChart = Line(init_opts=init_opts)
    xaxis = [item['cycle'] for item in stage_raw_data]
    chart.add_xaxis(xaxis)
    all_keys = set()
    for task in task_detail_data:
        all_keys.update(task.keys())
    # 只选择非零的键
    for key in [key for key in all_keys if any(task.get(key, 0) != 0 for task in task_detail_data)]:
        yaxis_data = [task.get(key, 0) for task in task_detail_data]
        chart.add_yaxis(key, yaxis_data, stack='task')
    lineChart.add_xaxis(xaxis)
    lineChart.add_yaxis(
        "Total Usage(%)",
        y_axis=[
            item['cpu_loading'] for item in stage_raw_data],
        is_smooth=True,  # 使用平滑曲线
        linestyle_opts=options.LineStyleOpts(width=3),  # 设置曲线粗细
    )

    chart.set_global_opts(
        title_opts=options.TitleOpts(title="任务执行情况"),
        legend_opts=options.LegendOpts(
            pos_top="6%"
        ),
        yaxis_opts=options.AxisOpts(
            axislabel_opts=options.LabelOpts(formatter=JsCode(
                "function(value) {return value + '%'}"))
        )
    )

    chart.overlap(lineChart)
    grid = Grid()
    grid.add(chart, grid_opts=options.GridOpts(pos_top='10%'))
    return chart.render_embed()


def renderPowerChart(*, report_path: str, stage: int) -> None:
    """
    此 Python 函数根据特定阶段的循环功率数据呈现折线图，并具有 x 轴、y 轴和系列的自定义选项。

    @param report_path `report_path` 参数是一个字符串，表示 `cycle_power_data.json` 文件所在目录的路径。该文件包含用于生成功率图表的原始数据。
    @param stage `stage` 参数代表您想要渲染功率图表的特定阶段。它用于根据提供的阶段编号从“raw_data”中过滤数据。

    @return 函数“renderPowerChart”返回嵌入的 HTML 代码，用于根据从特定阶段的“cycle_power_data.json”文件中提取的数据呈现折线图。
    """
    with open(f'{report_path}/cycle_power_data.json', 'r', encoding='utf-8') as f:
        raw_data = json.load(f)
    stage_raw_data = []

    stage_raw_data = [item for item in raw_data if item['stage'] == stage]
    # 设置图片大小
    lineChart = Line(
        init_opts=options.InitOpts(
            width='800px', height='200px', chart_id="chart")
    )
    lineChart.add_xaxis(
        [item['time'] for item in stage_raw_data])
    lineChart.add_yaxis(
        f'{"wakeup" if stage == 1 else "power"} status',
        [item['status'] for item in stage_raw_data],
        is_step=True,
        is_symbol_show=False,
    )
    lineChart.set_global_opts(
        xaxis_opts=options.AxisOpts(
            type_="category", boundary_gap=False),
        yaxis_opts=options.AxisOpts(
            interval=1,
            axislabel_opts=options.LabelOpts(formatter=JsCode(
                "function(value) {return value === 0 ? 'off' : 'on'}"))
        ),
    )
    lineChart.set_series_opts(
        step='end'
    )

    return lineChart.render_embed()

def renderStackUsageChart(*, report_path: str, stage: int) -> None:
    with open(f'{report_path}/cycle_report.json', 'r', encoding='utf-8') as f:
        raw_data = json.load(f)
    stage_raw_data = [item for item in raw_data if item['mode'] == stage]
    result = {}
    for item in stage_raw_data:
        for key, value in item['stack_usage'].items():
            # if key not in result:
            #     result[key] = value
            # else:
            #     result[key] = value if result[key] < value else result[key]
            if key == 'SysInit':
                continue
            result.setdefault(key, value)
            result[key] = max(result[key], value)

    # 设置图片大小
    chart = Bar(
        init_opts=options.InitOpts(
            width='800px', height='480px', chart_id="chart")
    )
    chart.add_xaxis(list(result.keys()))
    chart.add_yaxis('Stack Usage(%)', list(result.values()))
    chart.set_global_opts(
        title_opts=options.TitleOpts(title="Task Stack Usage"),
        xaxis_opts=options.AxisOpts(
            axislabel_opts=options.LabelOpts(rotate=55)  # 设置标签旋转角度为45度
        ),
        yaxis_opts=options.AxisOpts(
            axislabel_opts=options.LabelOpts(formatter=JsCode(
                "function(value) {return value + '%'}"))
        ),
    )
    return chart.render_embed()


def renderRestStackUsageChart(*, report_path: str, stage: int) -> None:
    with open(f'{report_path}/cycle_report.json', 'r', encoding='utf-8') as f:
        raw_data = json.load(f)
    with open('assets/stack_config.json', 'r', encoding='utf-8') as f:
        config_data = json.load(f)
    stage_raw_data = [item for item in raw_data if item['mode'] == stage]
    result = {}
    for item in stage_raw_data:
        for key, value in item['stack_usage'].items():
            # if key not in result:
            #     result[key] = value
            # else:
            #     result[key] = value if result[key] < value else result[key]
            if key == 'SysInit':
                continue
            result.setdefault(key, config_data[key] * (100 - value) / 100)
            result[key] = max(result[key], config_data[key]
                              * (100 - value) / 100)

    # 设置图片大小
    chart = Bar(
        init_opts=options.InitOpts(
            width='800px', height='450px', chart_id="chart"),
    )
    chart.add_xaxis(list(result.keys()))
    chart.add_yaxis('KBytes', list(result.values()))
    chart.set_global_opts(
        title_opts=options.TitleOpts(title="Stack rest space"),
        xaxis_opts=options.AxisOpts(
            axislabel_opts=options.LabelOpts(rotate=55)  # 设置标签旋转角度为45度
        ),
        yaxis_opts=options.AxisOpts(
            axislabel_opts=options.LabelOpts(formatter=JsCode(
                "function(value) {return value + 'KBytes'}"))
        ),
        # legend_opts=options.LegendOpts(
        #     pos_right="right"
        # ),
    )
    return chart.render_embed()

def renderHeapUsageChart(*, report_path: str, stage: int) -> None:
    with open(f'{report_path}/cycle_report.json', 'r', encoding='utf-8') as f:
        raw_data = json.load(f)
    stage_raw_data = [item for item in raw_data if item['mode'] == stage]
    result = {}
    for item in stage_raw_data:
        key = item['cycle']
        value = item['heap_usage']
        result.setdefault(key, value)
        result[key] = max(result[key], value)

    # 设置图片大小
    chart = Bar(
        init_opts=options.InitOpts(
            width='800px', height='450px', chart_id="chart")
    )
    chart.add_xaxis(list(result.keys()))
    chart.add_yaxis('RAM Usage(%)', list(result.values()))
    chart.set_global_opts(
        title_opts=options.TitleOpts(title="RAM Usage"),

        yaxis_opts=options.AxisOpts(
            axislabel_opts=options.LabelOpts(formatter=JsCode(
                "function(value) {return value + '%'}"))
        ),
    )
    return chart.render_embed()
def start_echarts_server(assets_path: str = r'D:\Project\pyecharts-assets'):
    """
    如果端口 8000 尚未使用，函数 `start_echarts_server` 将使用指定的资产路径启动 Echarts 服务器。

    @param assets_path
    start_echarts_server函数中的assets_path参数是一个字符串，代表Echarts资产所在的目录路径。在本例中，它设置为“D:\Project\pyecharts-assets”。该目录包含Echarts渲染所必需的文件
    """
    if is_port_in_use(8000):
        print("[Echarts]: Echarts Server is already started!")
    else:
        print("[Echarts]: Starting Echarts Server...")
        subprocess.Popen(
            f'python -m http.server --directory {assets_path}', shell=True)


if __name__ == '__main__':
    renderRestStackUsageChart(report_path=r'reports\2024-06-21\soc', stage=3)
    print("--")