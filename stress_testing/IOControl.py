from UART_COMM import UART_COMM
from time import sleep
from utils import singleton
import argparse


@singleton
# `IOControl` 类管理与 UART 设备的通信，以控制 KL30 和 KL15 信号的状态。
class IOControl:
    def __init__(self, port="COM5", baudrate=9600) -> None:
        """
        此 Python 函数使用默认端口“COM5”和波特率 9600 初始化 UART 通信对象。
        
        @param port `__init__`方法中的`port`参数用于指定用于UART通信的通信端口。在这种情况下，默认端口设置为“COM5”。您可以更改此端口以匹配系统上连接 UART
        通信的端口。
        @param baudrate “波特率”参数指定 UART 通信的通信速度。在本例中，默认波特率设置为 9600 位每秒。该参数决定通过 UART 接口在设备之间传输数据的速度。
        """
        self.uart = UART_COMM(port=port, baudrate=baudrate)

    def set_kl30(self, isOpen: bool):
        """
        此 Python 函数设置 KL30 的状态并通过 UART 发送相应的消息。
        
        @param isOpen set_kl30 方法中的 isOpen 参数是一个布尔值，指示 KL30 是打开还是关闭。当“isOpen”为“True”时，表示 KL30
        已打开，为“False”时，表示 KL30 已打开
        """
        sleep(1)
        if isOpen:
            self.uart.send_hex_message('A0 01 01 A2')
            print("[IO]: KL30 is open now!")
        else:
            self.uart.send_hex_message('A0 01 00 A1')
            print("[IO]: KL30 is closed now!")

    def set_kl15(self, isOpen: bool):
        """
        此 Python 函数根据 isOpen 参数设置 KL15 信号的状态，并通过 UART 发送相应的消息。
        
        @param isOpen set_kl15 方法中的 isOpen 参数是一个布尔值，指示 KL15 是打开还是关闭。当“isOpen”为“True”时，表示 KL15
        打开，当为“False”时，表示 KL15 关闭。
        """
        sleep(1)
        if isOpen:
            self.uart.send_hex_message('A0 02 01 A3')
            print("[IO]: KL15 is open now!")
        else:
            self.uart.send_hex_message('A0 02 00 A2')
            print("[IO]: KL15 is closed now!")

    def set_center_control(self, isOpen: bool):
        sleep(1)
        if isOpen:
            self.uart.send_hex_message('A0 03 01 A4')
            print("[IO]: Center control is open now!")
        else:
            self.uart.send_hex_message('A0 03 00 A3')
            print("[IO]: Center control is closed now!")


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='IO Control')
    parser.add_argument('--kl30', action='store_true', help='Enable kl30')
    parser.add_argument('--kl15', action='store_true', help='Enable kl15')
    parser.add_argument('--control-center',
                        action='store_true', help='Enable center control')
    parser.add_argument('--com', default='COM4')
    args = parser.parse_args()
    kl15 = args.kl15
    kl30 = args.kl30
    control_center = args.control_center
    com = args.com
    io_control = IOControl(port=com)
    io_control.set_kl30(kl30)
    io_control.set_kl15(kl15)
    io_control.set_center_control(control_center)
