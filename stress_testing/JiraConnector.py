from datetime import datetime, timedelta
import json
import requests
from requests.auth import HTTPBasicAuth


def create_bug_template(*, assignee: str = '712020:14b2381d-4382-4e43-991e-ed0b40ac45e9', description: str, duedate: str, fixVersions=[{"id": "10023"}], summary: str):
    """
    函数“create_bug_template”为特定项目管理工具生成 JSON 错误模板。
    
    @param assignee `create_bug_template` 函数中的 `assignee` 参数用于指定被分配处理 bug 的人员的用户 ID。默认情况下，受让人设置为
    '712020:14b2381d-4382-4e43-991
    @param description
    “create_bug_template”函数中的“description”参数用于提供所报告的错误或问题的详细描述。此描述应包括相关信息，例如重现问题的步骤、预期行为、实际行为以及有助于理解和解决问题的任何其他详细信息。
    @param duedate `create_bug_template` 函数中的 `duedate` 参数用于指定要修复的 bug
    的截止日期或截止日期。它应该以特定格式的字符串形式提供，表示需要解决错误的日期和时间。例如，它可以
    @param fixVersions `create_bug_template` 函数中的 `fixVersions` 参数用于指定预计修复 bug
    的版本。在提供的代码片段中，`fixVersions`参数默认设置为`{"id": "10023"}`。
    @param summary 您提供的“create_bug_template”函数似乎是在系统中创建错误的模板。该函数的参数如下：
    
    @return 函数“create_bug_template”返回一个表示错误模板的 JSON 对象，其中包含所提供的参数，例如受让人、描述、到期日期、修复版本和摘要。 JSON
    对象包括受让人、组件、描述、截止日期、问题类型、父项、项目、报告者和摘要等字段。注释掉的行表示不包括 fixVersions 和 versions 字段
    """
    return json.dumps({
        "fields": {
            "assignee": {
                "id": assignee
            },
            "components": [
                {
                    "id": "10007"
                }
            ],
            "description": {
                "content": [
                    {
                        "content": [
                            {
                                "text": description,
                                "type": "text"
                            }
                        ],
                        "type": "paragraph"
                    }
                ],
                "type": "doc",
                "version": 1
            },
            "duedate": duedate,
            # "fixVersions": fixVersions,
            "issuetype": {
                "id": "10050"
            },
            "parent": {
                "key": "TNM-538"
            },
            "project": {
                "id": "10017"
            },
            "reporter": {
                "id": assignee
            },
            "summary": f"[Stress Testing Bug]{summary}",
            # "versions": fixVersions
        }
    })


# “JiraConnector”类提供了与 Jira API 交互以检索和创建问题的方法。
class JiraConnector:
    url = "https://bitech-automotive.atlassian.net"
    auth = HTTPBasicAuth(
        "<EMAIL>",
        "ATATT3xFfGF07QzxRSydcDbez6mC6cd1rdvx1-IOLHT2t44aGR71oXa3FGfWXQi_zyLbMbDVXXl-kQZHHx_OxRKuJBxM3xCKKmS9uhJjLNwj7nXeF6gpA1CsW_bxaGlApkPHBWktR68Ra1wz3oBaKCw-a6L0nFvylSS5deyfO34_akI8gQuzMno=92BCD37F"
    )
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json"
    }

    def __init__(self) -> None:
        """
        上面的函数用特定值初始化了两个属性“total”和“max_page”。
        """
        self.total = 10000
        self.max_page = 0

    def get_max_pages(self) -> int:
        """
        此函数检索给定问题集的最大页数。
        
        @return “get_max_pages”方法在调用“get_issues”方法后返回对象的“max_page”属性。
        """
        self.get_issues()
        return self.max_page

    def get_issues(self, page_num: int = 0, page_size=100) -> list[str]:
        """
        此函数根据指定的页码和页面大小从 Jira 实例检索问题摘要列表。
        
        @param page_num “get_issues”方法中的“page_num”参数用于指定要检索的结果的页码。它是一个整数参数，如果未提供，则默认为
        0。该参数用于根据“page_size”计算要获取的结果的起始索引
        @param page_size “get_issues”函数中的“page_size”参数指定每页要检索的问题数。在本例中，“page_size”的默认值设置为
        100，这意味着默认情况下，该函数将检索每页 100 个问题，除非使用不同的值
        
        @return 正在返回问题摘要列表。
        """
        response = requests.request(
            "GET",
            self.url + '/rest/api/3/search',
            headers=self.headers,
            params={
                'maxResults': page_size,
                'fields': 'summary',
                'startAt': page_num * page_size,
            },
            auth=self.auth
        )
        results = response.json()['issues']
        self.max_page = int(int(response.json()['total']) / 100) + 1
        return [issue['fields']['summary'] for issue in results]

    def create_bug(self, summary: str, description: str) -> str:
        """
        此函数会在 Jira 中创建一个错误，并带有指定的摘要和描述。
        
        @param summary 所报告的错误或问题的简短描述。它应该提供问题的简明概述。
        @param description “create_bug”函数中的“description”参数是一个字符串，表示 Jira
        中创建的错误的详细描述。此描述应为某人提供足够的信息来理解问题并可能重现该问题。它可以包括重现步骤、预期行为、实际行为
        """
        current_time = datetime.now().date()
        due_date_obj = current_time + timedelta(days=2)
        due_date = due_date_obj.strftime('%Y-%m-%d')
        response = requests.request(
            "POST",
            self.url + '/rest/api/3/issue',
            headers=self.headers,
            data=create_bug_template(duedate=due_date, summary=datetime.now(
            ).strftime('%Y-%m-%d') + summary, description=description),
            auth=self.auth
        )
        if response.status_code == 201:
            print('[Jira]: Bug created!')
        else:
            print(json.dumps(json.loads(response.text),
                  sort_keys=True, indent=4, separators=(",", ": ")))
            print('[JiraError]: Fail to create Bug!')


if __name__ == '__main__':
    jira_connector = JiraConnector()
    jira_connector.create_bug(summary=' Trace Issue', description='test')
