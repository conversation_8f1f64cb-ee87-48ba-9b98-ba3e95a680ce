import lark_oapi as lark
from lark_oapi.api.im.v1 import *
from LarkRobot.config import robot, chat_id
class LarkRobot:
    def __init__(self) -> None:
        self.client = (
            lark.Client.builder()
            .app_id(robot["app_id"])
            .app_secret(robot["app_secret"])
            .log_level(lark.LogLevel.DEBUG)
            .build()
        )
    def __upload_file(self, report_name: str, report_path):
        file = open(report_path, "rb")
        request: CreateFileRequest = (
            CreateFileRequest.builder()
            .request_body(
                CreateFileRequestBody.builder()
                .file_type("pdf")
                .file_name(report_name)
                .file(file)
                .build()
            )
            .build()
        )
        # 发起请求
        response: CreateFileResponse = self.client.im.v1.file.create(request)
        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"client.im.v1.file.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
            )
            return
        print("file_key:", response.data.file_key)
        return response.data.file_key
    def send_msg(self, msg: str):
        request: CreateMessageRequest = (
            CreateMessageRequest.builder()
            .receive_id_type("chat_id")
            .request_body(
                CreateMessageRequestBody.builder()
                .receive_id("oc_673434411f8024d14c18f4469c976609")
                .msg_type("post")
                .content(
                    """{
                        \"zh_cn\": {
                            \"title\": \"Chery T1E-SOC压力测试测试报告（简洁版）\",
                            \"content\": %s
                        }
                    }"""
                    % msg
                )
                .build()
            )
            .build()
        )
        # 发起请求
        response: CreateMessageResponse = self.client.im.v1.message.create(request)
        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"client.im.v1.message.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
            )
            return
        # 处理业务结果
        lark.logger.info(lark.JSON.marshal(response.data, indent=4))
    def send_report(self, report_name: str, report_path: str):
        file_key = self.__upload_file(report_path=report_path, report_name=report_name)
        request: CreateMessageRequest = (
            CreateMessageRequest.builder()
            .receive_id_type("chat_id")
            .request_body(
                CreateMessageRequestBody.builder()
                .receive_id("oc_d18c36bca15c5c28fd6262b1fea02698")
                .msg_type("file")
                .content('{"file_key":"%s"}' % file_key)
                .build()
            )
            .build()
        )
        # 发起请求
        response: CreateMessageResponse = self.client.im.v1.message.create(request)
        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"client.im.v1.message.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
            )
            return
        # 处理业务结果
        lark.logger.info(lark.JSON.marshal(response.data, indent=4))
if __name__ == "__main__":
    robot = LarkRobot()
    robot.send_msg(msg="Hello world")
    robot.send_report(
        report_name="TestReport.pdf",
        report_path=r"D:\Projects\t1e\stress_testing\TestReport.pdf",
    )