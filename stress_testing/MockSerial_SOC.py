import serial
import time
from datetime import datetime

def get_timestamp():
    return datetime.now().strftime("[%m/%d %H:%M:%S:%f]")[:-3]

def send_message(serial_port, message):
    timestamped_message = f"{get_timestamp()} {message}"
    serial_port.write(timestamped_message.encode('utf-8'))
    print(timestamped_message)

def device_on_log(serial_port):
    message = """
Device on Log start:
***********************************
 T1E SWP1.40.0_RELEASE
 Compile Date: Jun 13 2024 09:31:10
***********************************
Device on Log end;
"""
    send_message(serial_port, message)

def ram_loading_log(serial_port):
    message = """
RAM loading log start:
RAM Loading: 52%
Memory free: 32614976 bytes.
RAM loading log end;
"""
    send_message(serial_port, message)

def cpu_loading_log(serial_port):
    message = """
CPU loading log start:
---------------------------------------------
Task                State   Priority     Stack    #
awtk                    X       1       32363   1
vg                      R       0       20387   19
IDLE                    R       0       374     14
CAM5ms                  B       29      922     3
Spi                     B       1       423     9
ucom                    B       27      3957    2
CAM10ms                 B       29      921     4
AWTK                    B       1       8095    8
CAM50ms                 B       29      941     6
CAM25ms                 B       29      941     5
CAM100ms                B       29      893     7
Tmr Svc                 B       31      934     15
usb_scan                B       31      1449    18
EGL_Worker              B       31      24468   22
usbread                 B       1       8093    23
Init/Deinit             B       27      343     10
animation               S       28      321     16
HighPrioEvent           B       30      409     11
InterruptHandler        B       24      24332   20
signal                  B       16      24468   21
LowPrioEvent            B       28      409     12
wq_otg                  B       31      1447    17
---------------------------------------------
Task                Abs Time          %Time
awtk                    6103            <1%
IDLE                    26747           1%
vg                      1931062         80%
AWTK                    3294            <1%
Tmr Svc                 199120          8%
CAM100ms                11953           <1%
CAM5ms                  11416           <1%
ucom                    87672           3%
Spi                     5094            <1%
CAM25ms                 1487            <1%
CAM50ms                 778             <1%
CAM10ms                 42106           1%
EGL_Worker              0               <1%
usbread                 0               <1%
Init/Deinit             243             <1%
animation               569             <1%
HighPrioEvent           0               <1%
LowPrioEvent            0               <1%
InterruptHandler        44208           1%
signal                  29824           1%
wq_otg                  0               <1%
usb_scan                0               <1%
---------------------------------------------
CPU loading log end;
"""
    send_message(serial_port, message)

def main():
    # 打开COM11端口
    serial_port = serial.Serial('COM11', 9600, timeout=1)
    
    start_time = time.time()
    
    while True:
        current_time = time.time()
        elapsed_time = current_time - start_time

        if elapsed_time % 120 < 1:
            device_on_log(serial_port)
        if elapsed_time % 20 < 1:
            ram_loading_log(serial_port)
        if elapsed_time % 40 < 1:
            cpu_loading_log(serial_port)
        
        time.sleep(1)

if __name__ == "__main__":
    main()
