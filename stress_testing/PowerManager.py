from time import sleep
import pyvisa as visa
import argparse


class PowerSupplyControl:
    """
    A class that provides methods for controlling a power supply device.
    Args:
        visa_address (str): The VISA address of the power supply device.
    Example Usage:
        power_supply = PowerSupplyControl("USB0::0x1AB1::0x0E11::DP8A253500492::INSTR")
        power_supply.enable_output(channel=1)
        power_supply.set_voltage(channel=1, voltage=6.0)
        power_supply.set_current(channel=1, current=2.0)
        sleep(1)
        voltage = power_supply.measure_voltage(channel=1)
        current = power_supply.measure_current(channel=1)
        print(f"Voltage: {voltage} V")
        print(f"Current: {current} A")
        power_supply.disable_output(channel=1)
        power_supply.close()
    """

    def __init__(self, visa_address):
        self.visa_address = visa_address
        self.rm = visa.ResourceManager()
        self.power_supply = self.rm.open_resource(visa_address)

    def set_voltage(self, channel: int, voltage: float):
        """
        Set the voltage of a specific channel.
        Args:
            channel (int): The channel number.
            voltage (float): The voltage to set.
        """
        self.power_supply.write(f":SOURce{channel}:VOLT {voltage}")

    def set_current(self, channel: int, current: float) -> None:
        """
        Set the current of a specific channel.
        Args:
            channel (int): The channel number.
            current (float): The current to set.
        """
        self.power_supply.write(f":SOURce{channel}:CURR {current}")

    def enable_output(self, channel: int):
        """
        Enable the output of a specific channel.
        Args:
            channel (int): The channel number.
        """
        self.power_supply.write(f":OUTP CH{channel}, ON")

    def disable_output(self, channel: int):
        """
        Disable the output of a specific channel.
        Args:
            channel (int): The channel number.
        """
        self.power_supply.write(f":OUTP CH{channel}, OFF")

    def measure_voltage(self, channel: int):
        """
        Measure the voltage of a specific channel.
        Args:
            channel (int): The channel number.
        Returns:
            float: The measured voltage.
        """
        return float(self.power_supply.query(f":MEAS:VOLTage? CH{channel}"))

    def measure_current(self, channel: int):
        """
        Measure the current of a specific channel.
        Args:
            channel (int): The channel number.
        Returns:
            float: The measured current.
        """
        return float(self.power_supply.query(f":MEAS:CURRent? CH{channel}"))

    def send_query(self, command: str) -> None:
        """
        Send a custom query command to the power supply device.
        Args:
            command (str): The query command to send.
        """
        self.power_supply.write(command)

    def query_value(self, command: str) -> any:
        """
        Query a value from the power supply device.
        Args:
            command (str): The query command to send.
        Returns:
            any: The queried value.
        """
        return self.power_supply.query(command)

    def close(self):
        """
        Close the connection to the power supply device.
        """
        self.power_supply.close()


# 示例用法
if __name__ == "__main__":
    # USB0::0x1AB1::0x0E11::DP8B223301309::INSTR
    parser = argparse.ArgumentParser(
        description="Control a power supply device.")
    parser.add_argument("--address", type=str,
                        help="The VISA address of the power supply device.")
    parser.add_argument("--channel", type=int, default=1,
                        help="The channel number (default: 1).")
    parser.add_argument("--voltage", type=float, default=0.0,
                        help="The voltage to set (default: 0.0).")
    args = parser.parse_args()
    address = args.address
    channel = args.channel
    voltage = args.voltage
    power_supply = PowerSupplyControl(address)
    power_supply.enable_output(channel=channel)
    power_supply.set_voltage(channel=channel, voltage=voltage)
    sleep(1)
    voltage = power_supply.measure_voltage(channel=channel)
    current = power_supply.measure_current(channel=channel)

    sleep(3)

    print(f"Voltage: {voltage} V")
    print(f"Current: {current} A")
    power_supply.disable_output(channel=1)
    power_supply.close()
