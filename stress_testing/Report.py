import json
from pyppeteer import launch
import pypandoc
import asyncio
from Echarts import renderRestStackUsageChart,renderHeapUsageChart, renderPowerChart, renderStackUsageChart, renderTaskDetail, start_echarts_server
import os
script_path = os.path.abspath(__file__)
script_directory = os.path.dirname(script_path)
# 获取当前工作目录
current_dir = os.getcwd()


class Report:
    def __init__(self, chrome_path: str):
        # 指定Chrome浏览器的路径
        self.chrome_path = chrome_path

    # 启动本地chrome

    async def start_chrome_engine(self):
        return await launch(executablePath=self.chrome_path)
    # 生成图表

    async def snapshot(self, html: str, browser, output_file_path: str, width: int = 800, height: int = 380):
        page = await browser.newPage()
        await page.setContent(html)
        await page.waitForSelector('#chart')
        await asyncio.sleep(1)
        # 设置截图区域的宽度和高度
        clip = {'x': 0, 'y': 0, 'width': width, 'height': height}
        await page.screenshot({"path": output_file_path, "clip": clip})

    async def generate_graph(self, report_path: str):
        browser = await self.start_chrome_engine()
        # with open(f'{report_path}/cycle_report.json', 'r', encoding='utf-8') as f:
        #     cycles = json.load(f)
        # 先生成html
        for stage in [1, 2, 3, 4]:
            # stage_data = [cycle for cycle in cycles if cycle['mode'] == stage]
            # html = renderChart(raw_data=stage_data, yaxis_list=[
            #                    'cpu_loading'], chart_type='bar', title=f'CPU Loading', xaxis='cycle')
            # await self.snapshot(html=html, browser=browser,
            #                     output_file_path=f'{report_path}/stage{stage}_bar_chart.png')
            # html = renderChart(raw_data=stage_data, yaxis_list=[
            #                    'cpu_loading'], chart_type='line', title=f'CPU Loading', xaxis='cycle', height='300px')
            # await self.snapshot(html=html, browser=browser,
            #                     output_file_path=f'{report_path}/stage{stage}_line_chart.png', height=300)
            html = renderPowerChart(report_path=report_path, stage=stage)
            await self.snapshot(html=html, browser=browser, output_file_path=f'{report_path}/stage{stage}_power_line_chart.png', height=200)
            html = renderTaskDetail(report_path=report_path, stage=stage)
            await self.snapshot(html=html, browser=browser, output_file_path=f'{report_path}/stage{stage}_task_detail_chart.png', height=480)
            html = renderStackUsageChart(report_path=report_path, stage=stage)
            await self.snapshot(html=html, browser=browser, output_file_path=f'{report_path}/stage{stage}_stack_usage_chart.png', height=480)
            html = renderRestStackUsageChart(report_path=report_path, stage=stage)
            await self.snapshot(html=html, browser=browser, output_file_path=f'{report_path}/stage{stage}_stack_rest_usage_chart.png', height=480)
            html = renderHeapUsageChart(report_path=report_path, stage=stage)
            await self.snapshot(html=html, browser=browser, output_file_path=f'{report_path}/stage{stage}_heap_usage_chart.png', height=480)

        await browser.close()

    async def generate_pdf(self, report_path: str):
        await self.generate_graph(report_path=report_path)
        browser = await self.start_chrome_engine()
        # 将markdown文件转换为html文件
        content = pypandoc.convert_file(
            f'{report_path}/report.md', to='html', format='md')
        with open(f'{report_path}/report.html', 'w', encoding='utf-8') as file:
            # 将文本写入文件
            file.write(content)
        # 创建一个新页面
        page = await browser.newPage()
        absolute_path = os.path.join(
            script_directory, f'{report_path}/report.html')
        await page.goto(f'file://{absolute_path}')
        # 通过pandoc的css绘制html
        await page.addStyleTag(path=os.path.join(current_dir, './assets/pandoc.css'))
        # 将页面保存为PDF
        await page.pdf({'path': f'{report_path}/report.pdf', 'format': 'A4'})
        # 关闭浏览器
        await browser.close()
        print(f'[Report]: pdf path:{report_path}/report.pdf')


async def generate_pdf_report():
    start_echarts_server()
    report = Report(
        chrome_path=r'C:\Program Files\Google\Chrome\Application\chrome.exe')
    # await report.generate_graph(report_path=r'reports\2024-02-26')

    await report.generate_pdf(report_path=r'D:\Project\stress_testing\reports\2024-02-26')
if __name__ == '__main__':
    asyncio.run(generate_pdf_report())
