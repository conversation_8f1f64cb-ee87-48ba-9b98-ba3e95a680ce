import asyncio
import json
import os
import subprocess
import sys
from IOControl import IOControl
from PowerManager import PowerSupplyControl
from time import sleep
from Canoe import CanoeRunner
from VideoRecorder import VideoRecorder
from datetime import datetime
from logparser_soc import LogParser_SOC
from logparser_mcu import LogParser_MCU
from Echarts import start_echarts_server
from Report import Report
from DingTalkRobot.DingTalkRobot import DingTalkRobot
from LarkRobot.LarkRobot import LarkRobot
from DingTalkRobot.message_template import create_message_template
from JiraConnector import JiraConnector
import argparse
import subprocess
import threading
import pytz
parser = argparse.ArgumentParser(description='T19C Stress Testing')
parser.add_argument('--repo-path', help='仓库路径', default=r'D:\Project\mcu')
parser.add_argument('--enable-cloud', help='启用云编译',
                    default=False, action='store_true')
parser.add_argument('--disable-flash-ext', help='不刷Ext Flash',
                    default=False, action='store_true')
parser.add_argument('--disable-notice', help='禁用钉钉消息通知',
                    default=False, action='store_true')
parser.add_argument('--disable-trace', help='禁用Jira事务追踪',
                    default=False, action='store_true')
parser.add_argument('--project', help='项目名', default="Chery T1E SOC")
parser.add_argument('--contacts', help='联系人',
                    default="Sun DianDian<a style='margin-left: 8px;'><EMAIL></a>")
parser.add_argument('--stage1-interval', help='休眠唤醒间隔',
                    default=20, required=True)
parser.add_argument('--stage1-total', help='休眠唤醒次数', default=6, required=True)
parser.add_argument('--stage2-interval', help='长时间负载间隔',
                    default=20, required=True)
parser.add_argument('--stage2-total', help='长时间负载周期数',
                    default=12, required=True)
parser.add_argument('--stage3-interval', help='上下电测试时间间隔',
                    default=20, required=True)
parser.add_argument('--stage3-total', help='长时间负载间隔',
                    default=12, required=True)
parser.add_argument('--stage4-interval', help='上下电测试时间间隔',
                    default=20, required=True)
parser.add_argument('--stage4-total', help='长时间负载间隔',
                    default=12, required=True)
parser.add_argument('--start-wait', help='上电读电流间隔', default=3)
parser.add_argument('--down-wait', help='下电读电流间隔', default=3)
parser.add_argument('--sleep-wait', help='休眠读电流间隔', default=15)
parser.add_argument('--wake-up-wait', help='唤醒读电流间隔', default=3)
parser.add_argument('--disable-flash', help="禁用刷写",
                    action='store_true', default=False)
parser.add_argument('--report-path', help='报告路径', required=True)
parser.add_argument('--use-public', help='使用公共盘集成',
                    action='store_true', default=False)
parser.add_argument('--disable-ser', help='禁用串口',
                    action="store_true", default=False)
parser.add_argument('--power-channel', help='电源通道', default=1)
parser.add_argument('--voltage', help='电压值', default=12)
parser.add_argument(
    '--test-mode', help='测试模式，压力测试stress，正常测试normal', default='stress')
args = parser.parse_args()
# ==== args ====
__HAS_CANOE__ = True
now = datetime.now()
# 设置东八区时区
tz = pytz.timezone('Asia/Shanghai')
# 将当前时间转换为东八区时间
now_east_8 = now.astimezone(tz)

current_time = now_east_8.strftime("%Y-%m-%d")
# stage1 params
stage1_interval = float(args.stage1_interval)
stage1_total = int(args.stage1_total)
# stage2 params
stage2_interval = float(args.stage2_interval)
stage2_total = int(args.stage2_total)
# stage3 params
stage3_interval = float(args.stage3_interval)
stage3_total = int(args.stage3_total)
stage4_interval = float(args.stage4_interval)
stage4_total = int(args.stage4_total)
start_wait = int(args.start_wait)
down_wait = int(args.down_wait)
sleep_wait = int(args.sleep_wait)
wake_up_wait = int(args.wake_up_wait)
contacts = args.contacts
project = args.project
enable_notice = not args.disable_notice
flash_ext = not args.disable_flash_ext
enable_trace = not args.disable_trace
report_path = fr'{args.report_path}\{current_time}'
report_path_soc = fr'{args.report_path}\{current_time}\soc'
report_path_mcu = fr'{args.report_path}\{current_time}\mcu'
video_path = fr'{report_path}\video'
repo_path = args.repo_path
enable_cloud = args.enable_cloud
use_public = args.use_public
disable_flash = args.disable_flash
disable_ser = args.disable_ser
power_channel = args.power_channel
voltage = args.voltage
test_mode = args.test_mode

power_data = []
error_list = []
power_log = []
pin_log = []
os.makedirs(video_path, exist_ok=True)
os.makedirs(report_path, exist_ok=True)
os.makedirs(report_path_soc, exist_ok=True)
os.makedirs(report_path_mcu, exist_ok=True)
# === Init Class====
io_control = IOControl(port="COM4")
# USB0::0x1AB1::0x0E11::DP8B223301309::INSTR from @Li Chong
power_manager = PowerSupplyControl(
    "USB0::0x1AB1::0x0E11::DP8B223301309::INSTR")
recorder = VideoRecorder.get_instance(
    output_path=video_path, camera_index=0)

if __HAS_CANOE__:
    if test_mode == 'stress':
        canoe_runner = CanoeRunner(
            cfgPath=r'C:\Users\<USER>\Desktop\Chery T19FL Brazil simulation 20210910\Chery T1E simulation10.cfg')
    elif test_mode == 'normal':
        canoe_runner = CanoeRunner(
            cfgPath=r'D:\StrssTest\Chery_T1E_Simulation_Normal\Chery T1E simulation10.cfg')

report = Report(
    chrome_path=r'C:\Program Files\Google\Chrome\Application\chrome.exe')
robot = LarkRobot()


# setting valtage
power_manager.set_voltage(channel=power_channel, voltage=voltage)

# record video
recorder.start_recording()

# start echarts server
start_echarts_server()
# ====== Function Declaration ======
lock = threading.Lock()
def power_log_catch(stop_event):
    global power_log
    while not stop_event.is_set():
        with lock:
            current = power_manager.measure_current(channel=power_channel)
            voltage = power_manager.measure_voltage(channel=power_channel)
            log_entry = f"{now_east_8.strftime('%H:%M:%S')}: current: {current}, voltage: {voltage}"
            power_log.append(log_entry)
        sleep(10)
stop_event = threading.Event()
thread = threading.Thread(target=power_log_catch, args=(stop_event,))
thread.start()
def close_device(mode: int, need_write_log: bool = True, cycle=1):
    if __HAS_CANOE__:
        canoe_runner.stop_measurement()
    io_control.set_kl15(isOpen=False)
    pin_log.append(now_east_8.strftime("%H:%M:%S")+":"+"KL15"+":"+"OFF")
    io_control.set_kl30(isOpen=False)
    pin_log.append(now_east_8.strftime("%H:%M:%S")+":"+"KL30"+":"+"OFF")
    sleep(start_wait)
    current = power_manager.measure_current(channel=power_channel)
    if current > 0.002:
        error_list.append(f"第{mode}阶段第{cycle}周期下电电流未降为0，实际电流值：{current}A")
        print(f"[PowerError]: Devices Down Fail! Current: {current}!")
    # write power on log
    if need_write_log:
        power_data.append({
            "time": now_east_8.strftime("%H:%M:%S"),
            "status": 0,
            "stage": mode
        })


def start_device(mode: int, need_write_log: bool = True, read_current=True, cycle=1):
    io_control.set_kl15(isOpen=True)
    pin_log.append(now_east_8.strftime("%H:%M:%S")+":"+"KL15"+":"+"ON")
    io_control.set_kl30(isOpen=True)
    pin_log.append(now_east_8.strftime("%H:%M:%S")+":"+"KL30"+":"+"ON")
    sleep(start_wait)
    if read_current:
        current = power_manager.measure_current(channel=power_channel)
        if current < 0.05:
            error_list.append(f"第{mode}阶段第{cycle}周期上电电流值异常，实际电流值{current}A")
            print(f"[PowerError]: Devices Start Fail! Current: {current}!")
    # write power on log
    if need_write_log:
        power_data.append({
            "time": now_east_8.strftime("%H:%M:%S"),
            "status": 1,
            "stage": mode
        })
    if __HAS_CANOE__:
        canoe_runner.start_measurement(test_mode=test_mode)


def sleep_device(mode: int, cycle=1):
    if __HAS_CANOE__:
        canoe_runner.stop_measurement()
    io_control.set_kl15(isOpen=False)
    pin_log.append(now_east_8.strftime("%H:%M:%S")+":"+"KL15"+":"+"OFF")
    sleep(sleep_wait)
    current = power_manager.measure_current(channel=power_channel)
    if current > 0.0005:
        error_list.append(f"第{mode}阶段第{cycle}周期休眠电流未降为0，实际电流值{current}A")
        print(f"[PowerError]: Current: {current}, Devices Sleep Fail!")
    # write power on log
    power_data.append({
        "time": now_east_8.strftime("%H:%M:%S"),
        "status": 0,
        "stage": mode
    })


def sleep_device_by_simulation(mode: int, cycle=1):
    if __HAS_CANOE__:
        canoe_runner.canoe.set_environment_variable_value(
            "Env_PowerMode", 5)
        sleep(2)
        canoe_runner.stop_measurement()

    sleep(2)
    io_control.set_kl15(isOpen=False)
    pin_log.append(now_east_8.strftime("%H:%M:%S")+":"+"KL15"+":"+"OFF")
    sleep(sleep_wait)
    current = power_manager.measure_current(channel=power_channel)
    if current > 0.0005:
        error_list.append(f"第{mode}阶段第{cycle}周期休眠电流未降为0，实际电流值{current}A")
        print(f"[PowerError]: Current: {current}, Devices Sleep Fail!")
    # write power on log
    power_data.append({
        "time": now_east_8.strftime("%H:%M:%S"),
        "status": 0,
        "stage": mode
    })


def wake_up_device_by_simulation(mode: int, cycle=1):
    io_control.set_kl15(isOpen=True)
    pin_log.append(now_east_8.strftime("%H:%M:%S")+":"+"KL15"+":"+"ON")
    if cycle == 1:
        io_control.set_kl30(isOpen=True)
        pin_log.append(now_east_8.strftime("%H:%M:%S")+":"+"KL30"+":"+"ON")
    sleep(2)
    if __HAS_CANOE__:
        canoe_runner.start_measurement(test_mode=test_mode)
        sleep(2)
        canoe_runner.canoe.set_environment_variable_value(
            "Env_PowerMode", 7)

    sleep(wake_up_wait)
    current = power_manager.measure_current(channel=power_channel)
    if current < 0.1:
        error_list.append(f"第{mode}阶段第{cycle}周期唤醒电流值异常，实际电流值{current}A")
        print(f"[PowerError]: Devices Wake Up Fail! Current: {current}!")
    # write power on log
    power_data.append({
        "time": now_east_8.strftime("%H:%M:%S"),
        "status": 1,
        "stage": mode
    })


def wake_up_device(mode: int, cycle=1):
    io_control.set_kl15(isOpen=True)
    pin_log.append(now_east_8.strftime("%H:%M:%S")+":"+"KL15"+":"+"ON")
    if cycle == 1:
        io_control.set_kl30(isOpen=True)
        pin_log.append(now_east_8.strftime("%H:%M:%S")+":"+"KL30"+":"+"ON")
    sleep(wake_up_wait)
    current = power_manager.measure_current(channel=power_channel)
    if current < 0.1:
        error_list.append(f"第{mode}阶段第{cycle}周期唤醒电流值异常，实际电流值{current}A")
        print(f"[PowerError]: Devices Wake Up Fail! Current: {current}!")
    # write power on log
    power_data.append({
        "time": now_east_8.strftime("%H:%M:%S"),
        "status": 1,
        "stage": mode
    })
    if __HAS_CANOE__:
        canoe_runner.start_measurement(test_mode=test_mode)


def write_power_data():
    with open(f"{report_path}/mcu/cycle_power_data.json", "w") as f:
        json.dump(power_data, f, indent=2, ensure_ascii=False)
    with open(f"{report_path}/soc/cycle_power_data.json", "w") as f:
        json.dump(power_data, f, indent=2, ensure_ascii=False)
    with lock:
        with open(f"{report_path}/soc/power_log.txt", "w") as f:
            for entry in power_log:
                f.write(entry + "\n")
    with open(f"{report_path}/soc/pin_log.txt", "w") as f:
        for entry in pin_log:
            f.write(entry + "\n")

close_device(mode=1, need_write_log=False)
start_device(mode=1, need_write_log=False)
close_device(mode=1, need_write_log=False)

# init
log_parser_soc = LogParser_SOC()
log_parser_soc.listen(port="COM7")
log_parser_soc.set_mode(1)

log_parser_mcu = LogParser_MCU()
log_parser_mcu.listen(port="COM9")
log_parser_mcu.set_mode(1)
# stage1 start


print("======stage1 start======")
cycle = 0
while cycle < stage1_total:
    print("[StageLog]: " + now_east_8.strftime("%H:%M:%S") +
          f": Cycle{cycle + 1} start testing!")
    wake_up_device(mode=1, cycle=cycle+1)
    if __HAS_CANOE__:
        canoe_runner.sleep_and_judge_is_down(
            mode=1, timer=int(stage1_interval * 60), interval=1)
    else:
        sleep(stage1_interval * 60)
    log_parser_soc.reset_cycles(disable_ser=disable_ser)

    log_parser_mcu.reset_cycles(disable_ser=disable_ser)
    sleep_device(mode=1, cycle=cycle+1)
    cycle += 1


# stage1 end
print("=======stage1 end=======")
if stage1_total:
    close_device(mode=1, need_write_log=False)

# stage2 start
print("======stage2 start======")
cycle = 0
log_parser_soc.set_mode(2)
log_parser_mcu.set_mode(2)
start_device(mode=2)
while cycle < stage2_total:
    print("[StageLog]: " + now_east_8.strftime("%H:%M:%S") +
          f": Cycle{cycle + 1} start testing!")
    if __HAS_CANOE__:
        canoe_runner.sleep_and_judge_is_down(
            mode=2, timer=int(stage2_interval * 60), interval=1)
    else:
        sleep(stage2_interval * 60)
    log_parser_soc.reset_cycles(disable_ser=disable_ser)
    log_parser_mcu.reset_cycles(disable_ser=disable_ser)
    cycle += 1

close_device(mode=2)

# stage2 end
print("=======stage2 end=======")
close_device(mode=2, need_write_log=False)

# stage3 start
print("======stage3 start======")
log_parser_soc.set_mode(3)
log_parser_mcu.set_mode(3)
cycle = 0
while cycle < stage3_total:
    print("[StageLog]: " + now_east_8.strftime("%H:%M:%S") +
          f": Cycle{cycle + 1} start testing!")
    start_device(mode=3, cycle=cycle+1)
    if __HAS_CANOE__:
        canoe_runner.sleep_and_judge_is_down(
            mode=3, timer=int(stage3_interval * 60), interval=1)
    else:
        sleep(stage3_interval * 60)
    log_parser_soc.reset_cycles(disable_ser=disable_ser)
    log_parser_mcu.reset_cycles(disable_ser=disable_ser)
    close_device(mode=3, cycle=cycle+1)
    cycle += 1

print("=======stage3 end=======")
close_device(mode=3, need_write_log=False)

print("======stage4 start======")

log_parser_soc.set_mode(4)
log_parser_mcu.set_mode(4)
cycle = 0
while cycle < stage4_total:
    print("[StageLog]: " + now_east_8.strftime("%H:%M:%S") +
          f": Cycle{cycle + 1} start testing!")
    wake_up_device_by_simulation(mode=4, cycle=cycle+1)
    if __HAS_CANOE__:
        canoe_runner.sleep_and_judge_is_down(
            mode=4, timer=int(stage1_interval * 60), interval=1)
    else:
        sleep(stage4_interval * 60)
    log_parser_soc.reset_cycles(disable_ser=disable_ser)

    log_parser_mcu.reset_cycles(disable_ser=disable_ser)
    sleep_device_by_simulation(mode=4, cycle=cycle+1)
    cycle += 1
print("=======stage4 end=======")
close_device(mode=4, need_write_log=False)
# generate
if __HAS_CANOE__:
    canoe_runner.stop_measurement()
    canoe_runner.quit_canoe()
    canoe_runner.write_can_down_log(log_path=report_path)
write_power_data()
recorder.stop_recording()

cycle_result_soc = log_parser_soc.generate_report(
    contacts=contacts, project="T1E_SOC", path=report_path_soc, video_path=video_path, errors='<br/>'.join(set(error_list)), disable_ser=disable_ser)
cycle_result_mcu = log_parser_mcu.generate_report(
    contacts=contacts, project="T1E_MCU", path=report_path_mcu, video_path=video_path, errors='<br/>'.join(set(error_list)), disable_ser=disable_ser)
log_parser_mcu.write_all_log(path=report_path_mcu)
log_parser_soc.write_all_log(path=report_path_soc)
if disable_ser:
    print("=======report generate success=======")
    sys.exit(0)
stop_event.set()
# 等待线程结束
thread.join()
# if __HAS_CANOE__:
asyncio.run(report.generate_pdf(report_path=report_path_soc))
asyncio.run(report.generate_pdf(report_path=report_path_mcu))
# if enable_notice:
# message = create_message_template(params_dict=cycle_result)

if enable_notice:
    if test_mode == 'normal':
        mode = "Normal-"
    else:
        mode = ""
    robot.send_report(
        report_name=f"{current_time}-{mode}TestReport-SOC.pdf",
        report_path=rf"{report_path_soc}/report.pdf",
    )
    robot.send_report(
        report_name=f"{current_time}-{mode}LOG-SOC.txt",
        report_path=rf"{report_path_soc}/soc_log.txt",
    )
    robot.send_report(
        report_name=f"{current_time}-{mode}TestReport-MCU.pdf",
        report_path=rf"{report_path_mcu}/report.pdf",
    )
    robot.send_report(
        report_name=f"{current_time}-{mode}LOG-MCU.txt",
        report_path=rf"{report_path_mcu}/mcu_log.txt",
    )
    robot.send_report(
        report_name=f"{current_time}-{mode}POWER-LOG.txt",
        report_path=rf"{report_path_soc}/power_log.txt",
    )
    robot.send_report(
        report_name=f"{current_time}-{mode}PIN-LOG.txt",
        report_path=rf"{report_path_soc}/pin_log.txt",
    )
