import serial
from time import sleep


class UART_COMM:
    def __init__(self, port="COM5", baudrate=115200) -> None:
        """
        The function initializes a serial connection with a specified port and baudrate.
        :param port: The "port" parameter specifies the communication port to which the serial device is
        connected. In this case, it is set to "COM5", which is a common port name for serial communication
        on Windows systems. However, the actual port name may vary depending on the specific setup and
        hardware configuration, defaults to COM5 (optional)
        :param baudrate: The baudrate parameter specifies the rate at which data is transmitted over the
        serial communication. It represents the number of bits per second (bps) that can be transmitted. In
        this case, the default baudrate is set to 115200 bits per second, defaults to 115200 (optional)
        """
        # 单例，全局仅实例化一次
        self.ser = serial.Serial(port=port, baudrate=baudrate)

    def send_text_message(self, message: str) -> None:
        print(f"\n[UART]: Send command: {message}\n")
        self.ser.write(message.encode('utf-8'))
        sleep(1)

    def read_single_DUT_log(self) -> str:
        """
        The function reads a single line from a serial port and returns it as a string.
        :return: a string.
        """
        try:
            return self.ser.readline().decode('utf-8')
        except Exception:
            return ""

    def read_multi_DUT_log(self, lines) -> list[str]:
        """
        The function reads a specified number of lines from a serial connection and returns them as a
        list of strings.
        :param lines: The `lines` parameter is the number of lines to read from the log file
        :return: a list of strings.
        """
        return [item.decode('utf-8') for item in self.ser.readlines(lines)]

    def send_hex_message(self, message):
        data = bytes.fromhex(message)
        self.ser.write(data)

    def close_connection(self) -> None:
        self.ser.close()
