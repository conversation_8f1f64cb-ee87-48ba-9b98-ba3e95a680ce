import cv2
import datetime
import threading
import queue
import atexit

# parser = argparse.ArgumentParser(description='VideoRecorder')

# # 添加命令行参数
# parser.add_argument('--index', help='选择摄像头', required=True, type=int)

# args = parser.parse_args()
# index = int(args.index)


class VideoRecorder:
    """
    A singleton class that allows recording video from a webcam and saving it to a specified output path.
    Attributes:
    - __instance: Stores the singleton instance of the VideoRecorder class.
    - is_stop: Indicates whether the recording process should be stopped.
    - output_path: The path where the recorded video will be saved.
    - recording: Indicates whether the video recording is currently in progress.
    - video_writer: The video writer object used to save the frames to a video file.
    - frame_queue: A queue used to store the frames to be saved to the video.
    - current_frame: The most recent frame captured from the webcam.
    """
    __instance = None
    is_stop = False

    def __init__(self, output_path: str, camera_index: int):
        """
        Initializes the VideoRecorder instance with the specified output path and sets up the necessary variables and threads.
        Args:
        - output_path: The path where the recorded video will be saved.
        """
        if VideoRecorder.__instance != None:
            raise Exception("VideoRecorder is a singleton class!")
        else:
            VideoRecorder.__instance = self
            self.output_path = output_path
            self.recording = False
            self.video_writer = None
            self.frame_queue = queue.Queue()
            self.current_frame = None
            self.camera_index = camera_index
            # Automatically call stop_recording() method when the program exits
            atexit.register(self.stop_recording)

    @staticmethod
    def get_instance(output_path: str, camera_index: int):
        """
        Returns the singleton instance of the VideoRecorder class. If the instance does not exist, it creates a new one.
        Args:
        - output_path: The path where the recorded video will be saved.
        Returns:
        - The singleton instance of the VideoRecorder class.
        """
        if VideoRecorder.__instance == None:
            VideoRecorder(output_path=output_path, camera_index=camera_index)
        return VideoRecorder.__instance

    def start_recording(self):
        """
        Starts the video recording process by initializing the video writer, starting the recording, saving, and monitoring threads.
        """
        if not self.recording:
            self.recording = True
            current_time = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            self.video_writer = cv2.VideoWriter(
                self.output_path + '/' + current_time +
                ".avi", cv2.VideoWriter_fourcc(*"XVID"), 25, (640, 480)
            )
            record_thread = threading.Thread(target=self.record_video)
            record_thread.start()
            save_thread = threading.Thread(target=self.save_video)
            save_thread.start()
            monitor_thread = threading.Thread(target=self.monitor_video)
            monitor_thread.start()

    def stop_recording(self):
        """
        Stops the video recording process by releasing the video writer and signaling the threads to stop.
        """
        self.is_stop = True
        if self.recording:
            self.recording = False
            self.frame_queue.put(None)
            if self.video_writer is not None:
                self.video_writer.release()


    def write_frame(self, frame):
        """
        Adds a frame to the frame queue for saving to the video.
        Args:
        - frame: The frame to be added to the frame queue.
        """
        if self.recording:
            self.frame_queue.put(frame)

    def record_video(self):
        """
        Continuously captures frames from the webcam and adds them to the frame queue until the recording is stopped.
        """
        cap = cv2.VideoCapture(self.camera_index)
        while self.recording:
            ret, frame = cap.read()
            if not ret:
                break
            self.current_frame = frame
            self.write_frame(frame)
        cap.release()

    def save_video(self):
        while self.recording:
            try:
                frame = self.frame_queue.get()
                if frame is None:
                    break
                if frame is not None:
                    self.video_writer.write(frame)
            except Exception as e:
                print(f"Error occurred during video saving: {e}")

    def monitor_video(self):
        """
        Displays the current frame being recorded in a window until the recording is stopped.
        """
        while self.recording:
            if self.current_frame is not None:
                cv2.imshow("Recording...", self.current_frame)
                cv2.waitKey(1)
        cv2.destroyAllWindows()


if __name__ == '__main__':
    output_path = "./logs"
    recorder = VideoRecorder.get_instance(
        output_path=output_path, camera_index=0)
    recorder.start_recording()
