<!-- 逻辑判断函数 -->
{% macro format(value) -%}
{%- if value == 'PASS' -%}
<span style="color: green;font-weight: bold;">PASS<span>
{%- elif value == 'FAIL' -%}
<span style="color: red;font-weight: bold;">FAIL</span>
{%- else -%}
<span style="color: orange;font-weight: bold;">WARNING</span>
{%- endif -%}
{%- endmacro %}

# SW Dailybuild Test Report

## 基本信息

| 测试项目  | {{project}} |
| :------- | :------------------- |
| 测试日期  | {{test_date}}        |
| 编译日期  | {{ compile_date }}   |
| 软件版本  | {{ sw_version }}   |
| 测试结果  | {{ format(final_result) }}   |
| 失败原因  | {{cause}}         |
| 风险项    | {{warning}}         |
| 联系人    | {{contacts}}         |

## 详细测试结果

### 第一阶段 - 周期性休眠唤醒压力测试

#### 休眠唤醒期示意图

![休眠唤醒示意图](./stage1_power_line_chart.png)

运行时长：{{stage1_runtime}}

| 测试项   | 预期行为 | 实际行为    | 测试结果    |
| :-------------------- | :-------------------- | :-------------------- | :---- |
| 系统异常复位 | 启动{{stage1_expected_reset_count}}次 | 启动{{stage1_reset_count}}次         | {{format(stage1_reset_result)}} |
| CPU Loading  | < {{stage1_cpu_threshold_value}}%    | {{stage1_cpu_loading}}               | {{format(stage1_cpu_result)}}
| RAM Usage  | < 80%   | {{stage1_heap_usage}}               | {{format(stage1_heap_usage_result)}}

![ddr usage and task load info](./stage1_task_detail_chart.png)
![heap usage chart](./stage1_heap_usage_chart.png)

### 第二阶段 - 高负载长时间压力测试

#### 高负载长时间电源情况示意图

![高负载长时间电源情况示意图](./stage2_power_line_chart.png)

运行时长：{{stage2_runtime}}

#### 测试结果

| 测试项 | 预期行为   | 实际行为 | 测试结果  |
| :-------------------- | :-------------------- | :-------------------- | :---- |
| 系统复位    | 启动1次 | 启动{{stage2_reset_count - stage2_expected_reset_count + 1}}次          | {{format(stage2_reset_result)}}  |                                  |
| CPU Loading  | < {{stage2_cpu_threshold_value}}%    | {{stage2_cpu_loading}}               | {{format(stage2_cpu_result)}}
| RAM Usage  | < 80%   | {{stage2_heap_usage}}               | {{format(stage2_heap_usage_result)}}
![ddr usage and task load info](./stage2_task_detail_chart.png)
![heap usage chart](./stage2_heap_usage_chart.png)

### 第三阶段 - KL30 周期性复位压力测试

#### KL30 复位周期示意图

![KL30 复位周期示意图](./stage3_power_line_chart.png)

运行时长：{{stage3_runtime}} 

#### 测试结果

| 测试项 | 预期行为   | 实际行为  | 测试结果   |
| :-------------------- | :-------------------- | :-------------------- | :---- |
| 系统复位    | 启动{{stage3_expected_reset_count}}次 | 启动{{stage3_reset_count}}次         | {{format(stage3_reset_result)}} |
| CPU Loading  | < {{stage3_cpu_threshold_value}}%    | {{stage3_cpu_loading}}               | {{format(stage3_cpu_result)}}
| RAM Usage  | < 80%   | {{stage3_heap_usage}}               | {{format(stage3_heap_usage_result)}}
![ddr usage and task load info](./stage3_task_detail_chart.png)
![heap usage chart](./stage3_heap_usage_chart.png)

### 第四阶段 - 休眠指令周期性复位压力测试

#### KL30 复位周期示意图

![KL30 复位周期示意图](./stage4_power_line_chart.png)

运行时长：{{stage4_runtime}} 

#### 测试结果

| 测试项 | 预期行为   | 实际行为  | 测试结果   |
| :-------------------- | :-------------------- | :-------------------- | :---- |
| 系统复位    | 启动{{stage4_expected_reset_count}}次 | 启动{{stage4_reset_count}}次         | {{format(stage4_reset_result)}} |
| CPU Loading  | < {{stage4_cpu_threshold_value}}%    | {{stage4_cpu_loading}}               | {{format(stage4_cpu_result)}}
| RAM Usage  | < 80%   | {{stage4_heap_usage}}               | {{format(stage4_heap_usage_result)}}
![ddr usage and task load info](./stage4_task_detail_chart.png)
![heap usage chart](./stage4_heap_usage_chart.png)