<!-- 逻辑判断函数 -->
{% macro process_cpu_value(value) -%}
{%- if value is string -%}
<span style="color: green;">PASS<span>
{%- elif value is number and value >= 80 -%}
<span style="color: red;">FAIL</span>
{%- else -%}
<span style="color: green;">PASS<span>
{%- endif -%}
{%- endmacro %}

{% macro process_ddr_value(value) -%}
{%- if value is string -%}
<span style="color: green;">PASS<span>
{%- elif value is number and value >= 60 -%}
<span style="color: red;">FAIL</span>
{%- else -%}
<span style="color: green;">PASS<span>
{%- endif -%}
{%- endmacro %}

{% macro process_final_result(value) -%}
{%- if value == False -%}
<span style="color: red;">FAIL</span>
{%- else -%}
<span style="color: green;">PASS<span>
{%- endif -%}
{%- endmacro %}

{% macro process_reset_count(expectedValue, value) -%}
{%- if value != expectedValue -%}
<span style="color: red;">FAIL</span>
{%- else -%}
<span style="color: green;">PASS<span>
{%- endif -%}
{%- endmacro %}

{% macro process_black_and_error(value) -%}
{%- if value > 0 -%}
<span style="color: orange;">WARNING</span>
{%- else -%}
<span style="color: green;">PASS<span>
{%- endif -%}
{%- endmacro %}

# SW Dailybuild Test Report

## 基本信息

| 测试项目                    | {{project}} |
| :-------------------------- | :------------------- |
| 测试日期                    | {{test_date}}        |
| 编译日期                    | {{ compile_date }}   |
| 软件版本                    | {{ sw_version }}   |
| 测试结果                    | {{ process_final_result(final_result) }}   |
| 联系人                      | {{contacts}}         |

## 详细测试结果

### 第一阶段 - 周期性休眠唤醒压力测试

#### 休眠唤醒期示意图

![休眠唤醒示意图](./stage1_power_line_chart.png)

运行时长：{{stage1_runtime}}

| 测试项   | 预期行为 | 实际行为    | 测试结果    |
| :-------------------- | :-------------------- | :-------------------- | :---- |
| 系统异常复位 | 启动{{stage1_expected_reset_count}}次 | 启动{{stage1_reset_count}}次         | {{process_reset_count(stage1_expected_reset_count, stage1_reset_count)}} |
| CPU Loading  | < {{stage1_cpu_threshold_value}}%    | {{stage1_cpu_loading}}               | {{process_cpu_value(stage1_max_cpu_loading)}}                                |
| DDR Usage    | < {{stage1_ddr_threshold_value}}%   | {{stage1_ddr_usage}}                 | {{process_ddr_value(stage1_max_ddr_usage)}}                                  |
| Errorlog     | 捕获到 0 个 Error                     | 捕获到{{stage1_error_count}}个 Error | {{process_black_and_error(stage1_error_count)}}                          |
| 卡死数量     | 捕获到 0 个 卡死                     | 捕获到 {{stage1_down_count}} 个卡死 | {{process_black_and_error(stage1_down_count)}}                            |

![ddr usage and task load info](./stage1_cpu_ddr_line_chart.png)
![ddr usage and task load info](./stage1_bar_chart.png)

### 第二阶段 - 高负载长时间压力测试

#### 高负载长时间电源情况示意图

![高负载长时间电源情况示意图](./stage2_power_line_chart.png)

运行时长：{{stage2_runtime}}

#### 测试结果

| 测试项 | 预期行为   | 实际行为 | 测试结果  |
| :-------------------- | :-------------------- | :-------------------- | :---- |
| 系统复位    | 启动1次 | 启动{{stage2_reset_count - stage2_expected_reset_count + 1}}次          | {{ process_reset_count(stage2_expected_reset_count, stage2_reset_count) }} |
| CPU Loading | < {{stage2_cpu_threshold_value}}%   | {{stage2_cpu_loading}}                | {{process_cpu_value(stage2_max_cpu_loading)}}                                  |
| DDR Usage   | < {{stage2_ddr_threshold_value}}%    | {{stage2_ddr_usage}}                  | {{process_ddr_value(stage2_max_ddr_usage)}}                                    |
| Errorlog    | 捕获到 0 个 Error                     | 捕获到{{stage2_error_count}} 个 Error | {{process_black_and_error(stage2_error_count)}}                            |
| 卡死数量     | 捕获到 0 个 卡死                     | 捕获到 {{stage2_down_count}} 个卡死 | {{process_black_and_error(stage2_down_count)}}                            |

![ddr usage and task load info](./stage2_cpu_ddr_line_chart.png)
![ddr usage and task load info](./stage2_bar_chart.png)

### 第三阶段 - KL30 周期性复位压力测试

#### KL30 复位周期示意图

![KL30 复位周期示意图](./stage3_power_line_chart.png)

运行时长：{{stage3_runtime}}

#### 测试结果

| 测试项 | 预期行为   | 实际行为  | 测试结果   |
| :-------------------- | :-------------------- | :-------------------- | :---- |
| 系统复位    | 启动{{stage3_expected_reset_count}}次 | 启动{{stage3_reset_count}}次         | {{ process_reset_count(stage3_expected_reset_count, stage3_reset_count)}} |
| CPU Loading | < {{stage3_cpu_threshold_value}}%    | {{stage3_cpu_loading}}               | {{ process_cpu_value(stage3_max_cpu_loading) }}                               |
| DDR Usage   | < {{stage3_ddr_threshold_value}}%    | {{stage3_ddr_usage}}                 | {{ process_ddr_value(stage3_max_ddr_usage) }}                                 |
| Errorlog    | 捕获到 0 个 Error                     | 捕获到{{stage3_error_count}}个 Error | {{process_black_and_error(stage3_error_count)}}                           |
| 卡死数量     | 捕获到 0 个 卡死                     | 捕获到 {{stage3_down_count}} 个卡死 | {{process_black_and_error(stage3_down_count)}}                            |

![ddr usage and task load info](./stage3_cpu_ddr_line_chart.png)
![ddr usage and task load info](./stage3_bar_chart.png)
