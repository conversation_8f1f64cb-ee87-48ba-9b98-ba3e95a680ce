from datetime import datetime
import json
from random import random
from threading import Thread
from time import sleep
import traceback
from UART_COMM import UART_COMM
from copy import deepcopy
from jinja2 import Template
import os
from utils import singleton
import pytz
current_dir = os.getcwd()

# Push请切换成False
__DEBUG_MODE__ = False


@singleton
class LogParser_MCU:
    compile_date = ""
    sw_version = ""
    error_flag = False
    cpu_flag = False

    def __init__(self) -> None:
        self.mode = 1
        self.end_flag = False
        self.cycle = 1
        self.uart_logs = []
        self.reports = []
        self.init_cycle_report = {
            "start_time": [],
            "end_time": "",
            "error_count": 0,
            "mode": 1,
            "down_count": 0,
            "cpu_loading": 0,
            "heap_usage": 0,
            "errors": [],
            "task_detail": {},
            "stack_usage": {
                "awtk": 27,
                "vg": 24,
                "Tmr Svc": 29,
                "Spi": 11,
                "CAM5ms": 33,
                "AWTK": 9,
                "ucom": 32,
                "CAM25ms": 24,
                "CAM100ms": 20,
                "CAM50ms": 18,
                "CAM10ms": 34,
                "wq_otg": 12,
                "usb_scan": 4,
                "LowPrioEvent": 24,
                "EGL_Worker": 18,
                "Init/Deinit": 20,
                "animation": 21,
                "InterruptHandler": 2,
                "signal": 5,
                "HighPrioEvent": 37},
        }
        # 周期报告
        self.cycle_report = deepcopy(self.init_cycle_report)

    def listen(self, port):
        """
        监听串口日志

        @param port 需要监听的端口
        """
        try:
            self.uart_comm = UART_COMM(port=port)
            log_thread = Thread(target=self.start_catch_log)
            log_thread.start()
        except Exception as e:
            traceback.print_exc()
            print(f"[LogParserError]: {e}")

    def set_mode(self, mode: int) -> None:
        """
        设置当前测试的模式。
        :param mode:
        :type mode: int
        """
        self.mode = mode
        self.cycle_report = deepcopy(self.init_cycle_report)
        self.cycle = 1

    def start_catch_log(self) -> None:
        # error log
        error_flag = False
        # reset log
        reset_flag = False
        # cpu load log
        cpu_load_flag = False

        ram_flag = False
        # Device on Log start:
        # Build Version: SWP01.10.01
        # Compile Date: 2024-01-29
        # Device on Log end;
        skip_next = False
        while not self.end_flag:
            # 捕获log并加上时间戳
            now = datetime.now()
            tz = pytz.timezone('Asia/Shanghai')
            now_east_8 = now.astimezone(tz)
            origin_log = self.uart_comm.read_single_DUT_log()
            log = f'[UART][{now_east_8.strftime("%H:%M:%S.%f")[:-3]}]{origin_log}'
            self.uart_logs.append(log)
            print("[MCU]", log)
            # catch task log
            if "Errlog log start" in origin_log:
                error_flag = True

            if "Errlog log end" in origin_log:
                error_flag = False
                self.error_flag = True
                # self.error_flag = False
                # self.cycle_report['error_count'] = error_count
            if error_flag:
                if "ErrorCnt" in origin_log:
                    self.cycle_report["error_count"] = int(
                        origin_log.split(":")[-1].strip()
                    )
                self.cycle_report["errors"].append(origin_log)

            # catch start log
            if "Device on Log start" in log:
                # print(log)
                cpu_load_flag = False
                reset_flag = True
                now = datetime.now()
                tz = pytz.timezone('Asia/Shanghai')
                now_east_8 = now.astimezone(tz)
                self.cycle_report["start_time"].append(
                    now_east_8.strftime("%H:%M:%S")
                )
                if len(self.cycle_report["start_time"]) > 1:
                    print("[LogParserError]: 异常复位！")
            if "Device on Log end" in log:
                reset_flag = False
            if "RAM loading log start" in log:
                ram_flag = True

            if "RAM loading log end" in log:
                ram_flag = False
            if reset_flag:
                if "Build Version" in log:
                    self.sw_version = log.split(":")[-1].strip()
                if "Compile Date" in log:
                    self.compile_date = log.split(":")[-1].strip()
            # catch cpu loading log
            if "CPU loading log start" in log:
                cpu_load_flag = True

            if "CPU loading log end" in log:
                cpu_load_flag = False
                self.cpu_flag = True
                # self.cpu_flag = False
            if ram_flag:
                if "RAM Loading" in log:
                    # print("[MCU]",log)
                    ram_usage = int(origin_log.split(" ")[2].replace("%", ""))
                    self.cycle_report["heap_usage"] = self.__comparative_value_size(
                        old_value=self.cycle_report["heap_usage"], new_value=ram_usage
                    )

            if cpu_load_flag:
                # if 'max loading' in log:
                #     self.cycle_report['cpu_loading'] = int(log.split(':')[
                #         -1].strip().replace('%', ''))
                # if skip_next:
                #     skip_next = False
                # elif "-" in log:
                #     skip_next = True
                if "Task" in log or "start" in log or "end" in log or "-" in log:
                    pass
                elif "%" in log.split()[-1]:
                    # print("[MCU]",log)
                    if "IDLE" in log:
                        cpu_loading = 100 - round(
                            float(
                                log.split()[-1]
                                .replace(" ", "")
                                .replace("<", "")
                                .replace("%", "")
                                .replace("\x00", "")
                            ),
                            2,
                        )
                        self.cycle_report["cpu_loading"] = (
                            self.__comparative_value_size(
                                old_value=self.cycle_report["cpu_loading"],
                                new_value=int(cpu_loading),
                            )
                        )
                    else:
                        if "<" not in log:
                            try:
                                int(origin_log.split()[1])
                                float(
                                    log.split()[-1]
                                    .replace(" ", "")
                                    .replace("%", "")
                                    .replace("\x00", "")
                                )

                                self.cycle_report["task_detail"][origin_log.split()[0]] = round(
                                    float(
                                        log.split()[-1]
                                        .replace(" ", "")
                                        .replace("%", "")
                                        .replace("\x00", "")
                                    ),
                                    2,
                                )
                            except (ValueError,IndexError):
                                if "Tmr Svc" in log:
                                    self.cycle_report["task_detail"][origin_log.split()[0]+origin_log.split()[1]] = round(
                                        float(
                                            log.split()[-1]
                                            .replace(" ", "")
                                            .replace("%", "")
                                            .replace("\x00", "")
                                        ),
                                        2,
                                    )

    def __close_log(self, path: str) -> None:
        """
        The function `__close_log` closes the log process, writes the log to a txt file, waits for 3
        seconds, sets the end flag to True, and closes the connection.
        :param path: The `path` parameter is a string that represents the file path where the log file
        will be saved
        :type path: str
        """
        # 输出log为txt文件
        # self.__write_all_log(path=path)
        # 关闭线程
        self.end_flag = True
        # 关闭连接
        self.uart_comm.close_connection()

    def close_log(self) -> None:
        """
        The function `__close_log` closes the log process, writes the log to a txt file, waits for 3
        seconds, sets the end flag to True, and closes the connection.
        :param path: The `path` parameter is a string that represents the file path where the log file
        will be saved
        :type path: str
        """
        # 输出log为txt文件
        # self.__write_all_log(path=path)
        # 关闭线程
        self.end_flag = True
        # 关闭连接
        self.uart_comm.close_connection()

    def resend_command_if_timeout(self, command: str, timeout: int = 5) -> None:
        self.uart_comm.send_text_message(command)
        count = 0
        resend_count = 0
        while True:
            while count < timeout:
                if self.cpu_flag or self.error_flag:
                    break
                count += 1
                sleep(1)
            # 超时
            if count >= timeout:
                self.uart_comm.send_text_message(command)
                count = 0
                resend_count += 1
                print(
                    f"\n[LogParser]: Resend {command} for {resend_count} times!\n")
            else:
                self.cpu_flag = False
                self.error_flag = False
                break

    def get_info(self) -> None:
        """
        异步发送命令获取error数量、loading情况、ddr使用情况
        """
        self.cycle_report["mode"] = self.mode
        # 获取errorLog
        self.resend_command_if_timeout(command="Term_errlog\n")
        sleep(3)
        # 获取CPU Loading
        self.resend_command_if_timeout(command="Term_sysinfo -l\n")
        sleep(10)
        self.resend_command_if_timeout(command="Term_sysinfo\n")
        sleep(10)

    def generate_report(
        self,
        contacts: str,
        project: str,
        path: str,
        video_path: str,
        errors: str,
        disable_ser: bool,
    ) -> None:
        """
        异步生成测试报告，包含整体log、周期log、测试报告
        :param contacts: 联系人
        :type contacts: str
        :param OEM: 供应商
        :type OEM: str
        :param project: 项目名
        :type project: str
        :param path: 存储路径
        :type path: str
        :param test_date 测试时间:
        :type test_date: str
        """
        try:
            with open(f"{path}/cycle_report.json", "w", encoding="utf-8") as file:
                json.dump(self.reports, file, indent=2, ensure_ascii=False)
            self.__write_all_log(path=path)
            if disable_ser:
                return
            template = Template(self.__read_report_template())
            params, final_result, warning, cause = self.analyse_stage_logs(
                report_path=path
            )
            now = datetime.now()
            tz = pytz.timezone('Asia/Shanghai')
            now_east_8 = now.astimezone(tz)
            params_dict = {k: v for d in params for k, v in d.items()} | {
                "project": project,
                "contacts": contacts,
                "compile_date": self.compile_date,
                "test_date": now_east_8.strftime("%Y-%m-%d %H:%M:%S"),
                "final_result": "PASS" if final_result and not errors else "FAIL",
                "build_date": self.compile_date,
                "sw_version": self.sw_version,
                "output_path": path,
                "warning": warning,
                "cause": cause + "<br/>" + errors,
                "video_path": video_path,
            }
            output = template.render(params_dict)
            with open(f"{path}/report.md", "w", encoding="utf-8") as file:
                file.write(output)
            self.__close_log(path)
            return params_dict

        except Exception as e:
            traceback.print_exc()
            print(f"[LogParserError]: Generate report error!\n{e}")

    def analyse_stage_logs(self, report_path: str):
        results = []
        test_result = True
        cause = []
        warning = []
        # 读取上下电周期数据
        with open(f"{report_path}/cycle_power_data.json", "r") as file:
            power_data = json.load(file)
        # 读取can中断
        # with open(f"{report_path}/can_down_log.json", "r") as file:
        #     can_down_data = json.load(file)
        for stage in [1, 2, 3, 4]:
            cpu_result = "PASS"
            stack_usage_result = "PASS"
            heap_usage_result = "PASS"
            stack_usage = []
            stage_log = [
                item for item in self.reports if item["mode"] == stage]
            stage_power_data = [
                item for item in power_data if item["stage"] == stage]
            # stage_can_down_data = [
            #     item for item in can_down_data if item["mode"] == stage
            # ]
            # sum error count
            # sum_error_count = sum(item['error_count']
            #                   for item in stage_log) if stage_log else 0
            min_error_count = (
                min(item["error_count"]
                    for item in stage_log) if stage_log else 0
            )
            max_error_count = (
                max(item["error_count"]
                    for item in stage_log) if stage_log else 0
            )
            # average cpu loading
            sum_cpu_loading = (
                int(sum(item["cpu_loading"]
                    for item in stage_log)) if stage_log else 0
            )
            average_cpu_loading = (
                int(int(sum_cpu_loading / len(stage_log))) if stage_log else 0
            )
            max_cpu_loading = (
                int(max(item["cpu_loading"]
                    for item in stage_log)) if stage_log else 0
            )
            sum_heap_usage = (
                int(sum(item["heap_usage"]
                    for item in stage_log)) if stage_log else 0
            )
            average_heap_usage = (
                int(int(sum_heap_usage / len(stage_log))) if stage_log else 0
            )
            max_heap_usage = (
                int(max(item["heap_usage"]
                    for item in stage_log)) if stage_log else 0
            )

            # stage_down_count = len(stage_can_down_data)
            if stage_power_data and stage_power_data[-1] and stage_power_data[0]:
                time_diff = datetime.strptime(
                    stage_power_data[-1]["time"], "%H:%M:%S"
                ) - datetime.strptime(stage_power_data[0]["time"], "%H:%M:%S")
            else:
                time_diff = "Invalid Date"
            # 计算阶段运行时间
            if type(time_diff) != str:
                hours, remainder = divmod(time_diff.total_seconds(), 3600)
                minutes, seconds = divmod(remainder, 60)
                runtime = f"{int(hours) if int(hours) >= 0 else 24 + int(hours)}小时 {int(minutes)}分 {int(seconds)}秒"
            else:
                runtime = time_diff
            # 判断是否异常复位
            expected_reset_count = len(stage_log)
            reset_count = sum(
                len(d["start_time"]) for d in stage_log if "start_time" in d
            )

            if (
                reset_count != expected_reset_count
                # or stage_down_count > 0
                or min_error_count != max_error_count
            ):
                if reset_count > expected_reset_count:
                    cause.append("异常复位")
                # if stage_down_count > 0:
                #     cause.append("can信号停发")
                if min_error_count != max_error_count:
                    cause.append("发现Error")
                test_result = False

            if expected_reset_count > reset_count and stage == 1:
                cause.append("无法休眠")
                test_result = False
            if max_cpu_loading > 90:
                cpu_result = "WARNING"
                warning.append("CPU过载")
            if max_heap_usage > 80:
                heap_usage_result = "WARNING"
                warning.append("RAM空间使用警告")
            for item in stage_log:
                for key, value in item["stack_usage"].items():
                    if value > 80 and key != "SysInit":
                        stack_usage_result = "WARNING"
                        stack_usage.append(f"{key}资源使用{value}%")
                        warning.append(f"{key}资源栈使用超出阈值")

            result = {
                f"stage{stage}_runtime": runtime if runtime else "NA",
                f"stage{stage}_expected_reset_count": (
                    expected_reset_count if expected_reset_count else 0
                ),
                f"stage{stage}_reset_count": reset_count if reset_count else 0,
                f"stage{stage}_cpu_loading": f"AVG: {average_cpu_loading}%/MAX: {max_cpu_loading}%",
                f"stage{stage}_max_cpu_loading": int(max_cpu_loading),
                f"stage{stage}_ddr_usage": "NA",
                f"stage{stage}_max_ddr_usage": "NA",
                f"stage{stage}_black_screen_count": 0,
                f"stage{stage}_error_count": max_error_count - min_error_count,
                # f"stage{stage}_down_count": stage_down_count,
                f"stage{stage}_ddr_threshold_value": 90,
                f"stage{stage}_cpu_threshold_value": 90,
                f"stage{stage}_reset_result": (
                    "PASS"
                    if reset_count == expected_reset_count and expected_reset_count != 0
                    else "FAIL"
                ),
                f"stage{stage}_errorLog_result": (
                    "PASS"
                    if max_error_count - min_error_count == 0
                    and expected_reset_count != 0
                    else "FAIL"
                ),
                # f"stage{stage}_down_result": (
                #     "PASS"
                #     if stage_down_count == 0 and expected_reset_count != 0
                #     else "FAIL"
                # ),
                f"stage{stage}_cpu_result": cpu_result,
                f"stage{stage}_stack_usage_result": stack_usage_result,
                f"stage{stage}_stack_usage": (
                    "<br/>".join(set(stack_usage))
                    if len(stack_usage)
                    else "每项使用 < 80%"
                ),
                f"stage{stage}_heap_usage": f"AVG: {average_heap_usage}%/MAX: {max_heap_usage}%",
                f"stage{stage}_max_heap_usage": int(max_heap_usage),
                f"stage{stage}_heap_usage_threshold_value": 80,
                f"stage{stage}_heap_usage_result": heap_usage_result,
            }
            results.append(result)
        return (
            results,
            test_result,
            "<br/>".join(set(warning)) or "无",
            "<br/>".join(set(cause)) or "无",
        )

    def reset_cycles(self, disable_ser) -> None:
        """
        The `reset_cycles` function resets the cycle report and initializes it with default values.
        """
        now = datetime.now()
        tz = pytz.timezone('Asia/Shanghai')
        now_east_8 = now.astimezone(tz)
        end_time = now_east_8.strftime("%H:%M:%S")
        # 记录截止时间
        self.cycle_report["end_time"] = end_time
        self.cycle_report["mode"] = self.mode
        self.cycle_report["cycle"] = f"Cycle{self.cycle}"
        self.reports.append(deepcopy(self.cycle_report))
        self.cycle += 1
        # 重置记录状态
        self.cycle_report = deepcopy(self.init_cycle_report)
        # 长时间负载 下周期开始时间为上周期结束时间
        if self.mode == 2:
            self.cycle_report["start_time"] = [end_time]
        sleep(10)

    def __read_report_template(self) -> str:
        """
        The function reads the contents of a Markdown template file and returns it as a string.
        :return: a string, which is the content of the file './dist/template.md'.
        """
        with open(
            os.path.join(current_dir, "./assets/template.md"), "r", encoding="utf-8"
        ) as file:
            template_str = file.read()
        return template_str

    def write_all_log(self, path: str) -> None:
        """
        The function writes all the elements in an array to a text file.
        :param path: The `path` parameter is a string that represents the directory path where the
        log.txt file will be created or updated
        :type path: str
        """
        # 将数组转换为字符串，每个元素占一行
        array_str = "".join(str(x) for x in self.uart_logs)
        # 将字符串写入txt文件
        with open(f"{path}/mcu_log.txt", "w") as f:
            f.write(array_str)

    def __comparative_value_size(self, old_value: int, new_value: int) -> int:
        if old_value >= new_value:
            return old_value
        else:
            return new_value
