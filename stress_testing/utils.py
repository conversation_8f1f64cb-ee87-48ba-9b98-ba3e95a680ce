import warnings


def deprecated(func):
    """
    The `deprecated` function is a decorator that can be used to mark a function as deprecated and issue
    a warning when it is called.

    :param func: The `func` parameter is a function that is being decorated with the `deprecated`
    decorator
    :return: The function `new_func` is being returned.
    """
    def new_func(*args, **kwargs):
        warnings.warn("Call to deprecated function {}.".format(
            func.__name__), category=DeprecationWarning)
        return func(*args, **kwargs)
    return new_func


def singleton(cls):
    """
    The `singleton` function is a decorator that ensures only one instance of a class is created and
    returned when the class is instantiated.
    :param cls: The `cls` parameter is a reference to the class that we want to make a singleton
    :return: The function `singleton` returns the `wrapper` function.
    """
    instances = {}

    def wrapper(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]
    return wrapper
