from JenkinsApi import JenkinsApi
import json
import datetime
import os
if __name__ == '__main__':
    jenkins = JenkinsApi(server_url='http://10.179.48.136:8081/', username='sup1whu', password='11399430c14cd6a8002f2060e9d2ad7c05')
    job_map ={
        "Compile_Mtk8678_Yocto_System_daily_build":"Compile_Chery/Compile_Mtk8678_Yocto_System_daily_build",
        "Compile_Mtk8678_Android_System_daily_build":"Compile_Chery/Compile_Mtk8678_Android_System_daily_build"
    }
    file_path = "logs"
    if os.path.exists(file_path) == False:
        os.mkdir(file_path)

    for key in job_map.keys():
        job_info ={}
        if key == "Compile_Mtk8678_Yocto_System_daily_build":
            last_number = 105
        elif key == "Compile_Mtk8678_Android_System_daily_build":
            last_number = 106
        for i in range(80,last_number+1):
            job_info_map = {
                "build_number":"",
                "build_time":"",
                "build_status":"",
                "build_console_output":"",
                "build_params":""
            }
            job_build_info =jenkins.get_build_info(job_map[key],i)
            job_info[f"build_{i}"] = job_build_info
        with open(f"{file_path}/{key}.json","w", encoding='utf-8') as f:
            json.dump(job_info,f,indent=4,ensure_ascii=False)
# print(jenkins.get_job_laster_build_number("QAC_SGMW/SGMW_202S_MCE_MY25_MCU"))
    








