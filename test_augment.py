import pygame
import pymunk
import pymunk.pygame_util
import math
import sys

# 初始化 pygame 和 pymunk
pygame.init()
width, height = 800, 800
screen = pygame.display.set_mode((width, height))
pygame.display.set_caption("球在旋转六边形内弹跳")
clock = pygame.time.Clock()

# 设置 pymunk 空间（物理世界）
space = pymunk.Space()
space.gravity = (0, 900)  # 模拟重力向下

draw_options = pymunk.pygame_util.DrawOptions(screen)

# 中心点
center = (width // 2, height // 2)

# 旋转参数
rotation_speed = 0.5  # 弧度每秒
angle = 0

# 创建六边形边界函数
def create_rotating_hexagon(radius=300):
    points = []
    for i in range(6):
        theta = math.radians(i * 60)
        x = radius * math.cos(theta)
        y = radius * math.sin(theta)
        points.append((x, y))
    return points

# 创建球体
def create_ball(position, radius=20, mass=1):
    body = pymunk.Body(mass, pymunk.moment_for_circle(mass, 0, radius))
    body.position = position
    shape = pymunk.Circle(body, radius)
    shape.elasticity = 0.9
    shape.friction = 0.5
    space.add(body, shape)
    return shape

# 创建六边形边界（使用 Segment 连接构建旋转外壳）
def create_hexagon_segments(points):
    body = pymunk.Body(body_type=pymunk.Body.KINEMATIC)
    body.position = center
    segments = []
    for i in range(len(points)):
        a = points[i]
        b = points[(i + 1) % len(points)]
        seg = pymunk.Segment(body, a, b, 5)
        seg.elasticity = 0.95
        seg.friction = 0.5
        segments.append(seg)
        space.add(seg)
    return body, segments

# 初始六边形
hexagon_points = create_rotating_hexagon()
hex_body, hex_segments = create_hexagon_segments(hexagon_points)

# 创建一个球
ball = create_ball((width // 2, height // 2 - 200))

# 主循环
running = True
while running:
    dt = clock.tick(60) / 1000.0  # 每帧时间（秒）

    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False

    # 更新六边形角度
    angle += rotation_speed * dt
    rotated_points = []
    for p in create_rotating_hexagon():
        x, y = p
        # 旋转点
        new_x = x * math.cos(angle) - y * math.sin(angle)
        new_y = x * math.sin(angle) + y * math.cos(angle)
        rotated_points.append((new_x, new_y))

    # 移除旧的 segments
    for seg in hex_segments:
        space.remove(seg)
    hex_segments.clear()

    # 创建新的 segments
    for i in range(len(rotated_points)):
        a = rotated_points[i]
        b = rotated_points[(i + 1) % len(rotated_points)]
        seg = pymunk.Segment(hex_body, a, b, 5)
        seg.elasticity = 0.95
        seg.friction = 0.5
        hex_segments.append(seg)
        space.add(seg)

    # 更新物理空间
    space.step(dt)

    # 绘图
    screen.fill((30, 30, 30))
    space.debug_draw(draw_options)
    pygame.display.flip()

pygame.quit()
sys.exit()
