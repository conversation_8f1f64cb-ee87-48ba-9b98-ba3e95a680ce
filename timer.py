from datetime import datetime, timezone, timedelta

# 给定的 Unix 时间戳（毫秒）
timestamp_ms = 1741691847185

# 转换为秒级时间戳
timestamp_s = timestamp_ms / 1000

# 转换为 UTC 时间
utc_time = datetime.fromtimestamp(timestamp_s, tz=timezone.utc)

# 转换为东八区时间（UTC+8）
china_tz = timezone(timedelta(hours=8))
china_time = utc_time.astimezone(china_tz)

# 输出结果
print("UTC 时间：", utc_time.strftime('%Y-%m-%d %H:%M:%S'))
print("北京时间（UTC+8）：", china_time.strftime('%Y-%m-%d %H:%M:%S'))

