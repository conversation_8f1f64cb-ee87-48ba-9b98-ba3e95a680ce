from datetime import datetime
import json
import re
import threading
from UART_COMM import UART_COMM, singleton
from copy import copy
from jinja2 import Template
import asyncio


@singleton
class SerialLogger:
    def __init__(self) -> None:
        self.mode = 0
        self.end_flag = False
        self.uart_logs = []
        self.uart_comm = UART_COMM()
        self.reports = []
        # 是否第一次启动
        self.is_first_start = True
        # 周期报告
        self.cycle_report = {
            "mode": 0,
            "start_time": [],
            "loading_info": [],
            "end_time": '',
            "current_space": 0,
            "minimal_space": 0,
            "ddr_usage": "",
            "cpu_loading": "",
            "error_log": [],
            "error_count": 0
        }
        # 出现usage信息的标志 -1 未出现 0 出现 >0 计数
        self.usage_info_flag = -1
        # 出现loading信息的标志 -1 未出现 0 出现 >0 计数
        self.loading_info_flag = -1
        # error_log标志
        self.error_log_flag = -1
        # 创建线程捕获log
        log_thread = threading.Thread(target=self.__listen)
        log_thread.start()

    def __listen(self):
        asyncio.run(self.start_catch_log())

    async def start_catch_log(self) -> None:
        while True:
            # 捕获log并加上时间戳
            origin_log = self.uart_comm.read_single_DUT_log()
            log = f'[{datetime.now().strftime("%H:%M:%S.%f")[:-3]}]{origin_log}'
            print(log)
            # 捕获errorlog
            self.__catch_error_log(origin_log)

            # 判断是否为启动日志
            if log.find('Firmware Software Version') != -1:
                await self.__catch_start_log(origin_log)
                continue

            # 如果在log中捕获到unit:bytes说明后两行是usage信息
            if 'unit:bytes' in log:
                self.usage_info_flag = 0
                # 处理运行一小时以上的特殊情况
                self.loading_info_flag = -1
                continue

            if 'task load(%)' in log:
                self.loading_info_flag = 0
                continue

            if self.usage_info_flag != -1:
                await self.__catch_usage_log(origin_log)

            if self.loading_info_flag != -1:
                await self.__catch_loading_log(origin_log)

    async def generate_loading_and_usage_log(self, mode: int) -> None:
        self.cycle_report['mode'] = mode
        await self.__wait_log_process()
        self.uart_comm.get_error_log_info()
        await self.__wait_log_process()
        self.uart_comm.get_loading_info()
        await self.__wait_log_process()
        self.uart_comm.get_usage_info()
        await asyncio.sleep(1)

    async def generate_report(self, contacts: str, OEM: str, project: str, path: str) -> None:
        await self.__wait_log_process()
        template = Template(await self.__read_report_template())

        params = await self.__analyse_stage_logs()
        params_dict = {k: v for d in params for k, v in d.items()} | {
            "OEM": OEM,
            "project": project,
            "contacts": contacts,
            "test_date": datetime.now().strftime("%Y-%m-%d-%H%M%S"),
        }
        print(params_dict)
        output = template.render(params_dict)
        with open(f'{path}/test_report.md', 'w', encoding='utf-8') as file:
            file.write(output)
        self.__save_as_json_file(f'{path}./test_report.json')

    async def __analyse_stage_logs(self) -> list[dict]:
        await self.__wait_log_process()
        results = []
        cpu_loading = "无数据"
        max_cpu_loading = "无数据"
        for stage in [1, 2, 3]:
            cpu_loading = "无数据"
            max_cpu_loading = "无数据"
            stage_log = None
            # 复位压力测试 取mode为0且有loading数据的
            if stage == 1:
                stage_log = [
                    item for item in self.reports if item['mode'] == 0 and item['cpu_loading']]
                cpu_loading_info = self.__calc_usage_info(
                    stage_log, 'cpu_loading')
                cpu_loading = cpu_loading_info['cpu_loading']
                max_cpu_loading = cpu_loading_info['max_cpu_loading']
            elif stage == 2:
                stage_log = [
                    item for item in self.reports if item['mode'] == 0 and not item['cpu_loading']]
            else:
                stage_log = [
                    item for item in self.reports if item['mode'] == 1]
                cpu_loading_info = self.__calc_usage_info(
                    stage_log, 'cpu_loading')
                cpu_loading = cpu_loading_info['cpu_loading']
                max_cpu_loading = cpu_loading_info['max_cpu_loading']
            error_count = stage_log[-1]['error_count'] if stage_log else 0
            time_diff = datetime.strptime(
                stage_log[-1]['end_time'], '%H:%M:%S') - datetime.strptime(stage_log[0]['start_time'][0], '%H:%M:%S') if stage_log else None
            # 提取数据
            runtime = f'{time_diff.seconds // 3600}小时 {time_diff.seconds // 3600}分 {time_diff.seconds % 60}秒' if time_diff else ""
            ddr_info = self.__calc_usage_info(stage_log, 'ddr_usage')
            expected_reset_count = len(stage_log)
            reset_count = sum(len(d["start_time"])
                              for d in stage_log if "start_time" in d)
            result = {
                f"stage{stage}_runtime": runtime,
                f"stage{stage}_expected_reset_count": expected_reset_count,
                f"stage{stage}_reset_count": reset_count,
                f"stage{stage}_cpu_loading": cpu_loading,
                f"stage{stage}_max_cpu_loading": float(max_cpu_loading[:-1]) if "%" in max_cpu_loading else "Not Found",
                f"stage{stage}_ddr_usage": ddr_info['ddr_usage'],
                f"stage{stage}_max_ddr_usage": ddr_info['max_ddr_usage'],
                f"stage{stage}_black_screen_count": 0,
                f"stage{stage}_error_count": error_count
            }
            results.append(result)
        return results

    def __calc_usage_info(self, stage_log: list, tag: str) -> dict:
        if not stage_log:
            return {tag: "AVG: /MAX: ", f'max_{tag}': 'NA'}
        max_value = max(stage_log, key=lambda x: float(
            x[tag].strip("%")))[tag]
        average_value = sum(
            float(d[tag].strip("%")) for d in stage_log
        ) / len(stage_log)
        value = f'AVG:{round(average_value, 2)}% / MAX:{max_value}'
        return {tag: value, f'max_{tag}': max_value}

    async def __reset_cycles(self, end_time: str) -> None:
        await self.__wait_log_process()
        # 记录截止时间
        self.cycle_report['end_time'] = end_time
        self.reports.append(copy(self.cycle_report))
        # 重置记录状态
        self.cycle_report = {
            "start_time": [end_time],
            "loading_info": [],
            "end_time": '',
            "current_space": 0,
            "minimal_space": 0,
            "ddr_usage": "",
            "cpu_loading": "",
            "error_log": [],
            "error_count": 0
        }

    async def __catch_start_log(self, log: str) -> None:
        await self.__wait_log_process()
        matches = re.search(r"\[(\d{2}:\d{2}:\d{2}(\.\d{3})?)\]", log)
        start_time = matches[0] if matches else datetime.now().strftime(
            "%H:%M:%S")
        if self.is_first_start == False and self.cycle_report['mode'] != 0:
            await self.__reset_cycles(start_time)

        self.cycle_report["start_time"].append(
            start_time)
        sw_version = log.split(
            "Firmware Software Version:[")[1].split("]")[0] or ""
        build_date = log.split("Build Date:[")[1].split("]")[
            0] if "Build Date" in log else "没有构建日期"
        self.cycle_report["sw_version"] = sw_version
        self.cycle_report["build_date"] = build_date
        # 第一次启动过了
        self.is_first_start = False

    def __catch_error_log(self, log: str) -> None:
        # 判断是否是错误信息
        if "Error Log" in log:
            self.cycle_report['error_log'].append(log)
            self.cycle_report['error_count'] += 1
            self.error_log_flag = 0
        else:
            self.error_log_flag = -1

    async def __catch_loading_log(self, log: str) -> None:
        await self.__wait_log_process()
        self.loading_info_flag += 1
        # 判断loading信息结束
        if self.loading_info_flag == 20:
            self.loading_info_flag = -1

        info = log.split()
        self.cycle_report['loading_info'].append({
            "task_name": info[0],
            "run_time": info[1],
            "task_load": info[2]
        })
        if 'IDLE' in info[0]:
            self.cycle_report['cpu_loading'] = "{:.2f}%".format(
                100 - int(info[2]))

    async def __catch_usage_log(self, log: str) -> None:
        await self.__wait_log_process()
        # sourcery skip: extract-method
        self.usage_info_flag += 1
        # 判断usage_log结束
        if self.usage_info_flag == 2:
            self.usage_info_flag = -1
        if "Minimal stack" in log:
            self.cycle_report['minimal_space'] = int(
                log.split(':')[1].replace('\r', '').replace('\n', ''))
            current_usage = self.cycle_report['current_space']
            minimal_usage = self.cycle_report['minimal_space']
            self.cycle_report['ddr_usage'] = "{:.2f}%".format((
                current_usage - minimal_usage) / current_usage * 100)
            end_time = datetime.now().strftime("%H:%M:%S")
            # 如果为长负载测试，重置周期数据
            if self.cycle_report['mode'] == 0:
                await self.__reset_cycles(end_time)
        else:
            self.cycle_report['current_space'] = int(
                log.split(':')[1].replace('\r', '').replace('\n', ''))

    async def __wait_log_process(self) -> None:
        while self.error_log_flag != -1 and self.loading_info_flag != -1 and self.usage_info_flag != -1:
            await asyncio.sleep(0.1)

    def __save_as_json_file(self, path: str) -> None:
        # 将嵌套字典转换为 JSON 格式的字符串
        json_str = json.dumps(self.reports, indent=2)
        # 将 JSON 字符串写入文件
        with open(path, 'w', encoding='utf-8') as file:
            file.write(json_str)

    async def __read_report_template(self) -> str:
        with open('./dist/template.md', 'r', encoding='utf-8') as file:
            template_str = file.read()
        return template_str
