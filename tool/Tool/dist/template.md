<!-- 逻辑判断函数 -->

{% macro process_value(value) -%}
{%- if value is string -%}
<span style="color: green;">PASS<span>
{%- elif value is number and value >= 85 -%}
<span style="color: red;">FAIL</span>
{%- else -%}
<span style="color: green;">PASS<span>
{%- endif -%}
{%- endmacro %}

{% macro process_reset_count(expectedValue, value) -%}
{%- if value != expectedValue -%}
<span style="color: red;">FAIL</span>
{%- else -%}
<span style="color: green;">PASS<span>
{%- endif -%}
{%- endmacro %}

{% macro process_black_and_error(value) -%}
{%- if value > 0 -%}
<span style="color: red;">FAIL</span>
{%- else -%}
<span style="color: green;">PASS<span>
{%- endif -%}
{%- endmacro %}

# SW Dailybuild Test Report

## 基本信息

| 测试项目                    | {{OEM}}\_{{project}} |
| --------------------------- | -------------------- |
| 测试日期（Dailybuild 版本） | {{test_date}}        |
| 测试结果                    | {{ final_result }}   |
| 联系人                      | {{contacts}}         |

## 详细测试结果

### 第一阶段 - KL30 周期性复位压力测试

运行时长：{{stage1_runtime}}

| 测试项       | 预期行为                        | 实际行为                      | 测试结果                                                                 |
| ------------ | ------------------------------- | ----------------------------- | ------------------------------------------------------------------------ |
| 系统异常复位 | 启动{{stage1_expected_reset_count}}次 | 启动{{stage1_reset_count}}次        | {{process_reset_count(stage1_expected_reset_count, stage1_reset_count)}} |
| CPU Loading  | < 85%                           | {{stage1_cpu_loading}}        | {{process_value(stage1_max_cpu_loading)}}                                |
| DDR Usage    | < 85%                           | {{stage1_ddr_usage}}          | {{process_value(stage1_max_ddr_usage)}}                                  |
| 系统黑屏     | 黑屏0次                          | 黑屏{{stage1_black_screen_count}}次 | {{process_black_and_error(stage1_black_screen_count)}}                   |
| Errorlog     | 捕获到0个Error                 | 捕获到{{stage1_error_count}}个Error        | {{process_black_and_error(stage1_error_count)}}                          |

### 第二阶段 - 高负载长时间压力测试

<!-- TODO 
#### KL30 复位周期示意图

Power/Timer Chart（请描画系统上电周期和时间的曲线图） 
-->

运行时长：{{stage2_runtime}}

#### 测试结果

| 测试项      | 预期行为                        | 实际行为                      | 测试结果                                                                   |
| ----------- | ------------------------------- | ----------------------------- | -------------------------------------------------------------------------- |
| 系统复位    | 启动{{stage2_expected_reset_count}}次 | 启动{{stage2_reset_count}}次        | {{ process_reset_count(stage2_expected_reset_count, stage2_reset_count) }} |
| CPU Loading | 无数据                          | {{stage2_cpu_loading}}        | {{process_value(stage2_max_cpu_loading)}}                                  |
| DDR Usage   | < 85%                           | {{stage2_ddr_usage}}          | {{process_value(stage2_max_ddr_usage)}}                                    |
| 系统黑屏    | 黑屏0次                          | 黑屏{{stage2_black_screen_count}}次 | {{process_black_and_error(stage2_black_screen_count)}}                     |
| Errorlog    | 捕获到0个Error                 | 捕获到{{stage2_error_count}} 个Error       | {{process_black_and_error(stage2_error_count)}}                            |
<!-- TODO
[^可选]:

CPU Loading、DDR Usage 在每个周期内的柱状图
-->
### 第三阶段 - 周期性休眠唤醒压力测试

#### 休眠唤醒期示意图
<!--  TODO
Wakeup-Sleep/Time Chart（请描画系统上电周期和时间的曲线图）
  -->
运行时长：{{stage3_runtime}}

#### 测试结果

| 测试项      | 预期行为                        | 实际行为                      | 测试结果                                                                  |
| ----------- | ------------------------------- | ----------------------------- | ------------------------------------------------------------------------- |
| 系统复位    | 启动{{stage3_expected_reset_count}}次 | 启动{{stage3_reset_count}}次        | {{ process_reset_count(stage3_expected_reset_count, stage3_reset_count)}} |
| CPU Loading | < 85%                           | {{stage3_cpu_loading}}        | {{ process_value(stage3_max_cpu_loading) }}                               |
| DDR Usage   | < 85%                           | {{stage3_ddr_usage}}          | {{ process_value(stage3_max_ddr_usage) }}                                 |
| 系统黑屏    | 黑屏0次                          | 黑屏{{stage3_black_screen_count}}次 | {{process_black_and_error(stage3_black_screen_count)}}                    |
| Errorlog    | 捕获到0个Error                 | 捕获到{{stage3_error_count}}个Error        | {{process_black_and_error(stage3_error_count)}}                           |

<!-- TODO
[^可选]:

CPU Loading、DDR Usage 在每个周期内的柱状图
 -->